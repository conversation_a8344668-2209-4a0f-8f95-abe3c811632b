﻿#ifndef YX_PERMISSION_H
#define YX_PERMISSION_H
#pragma once

#include "yxcppsdkdishcore_global.h"
#include <string>
#include <QString>  // 添加QString头文件
using namespace std;

#define QSTR(cstr) QString::fromLocal8Bit(cstr.c_str())
#define CSTR(qstr) std::string(qstr.toLocal8Bit())


class YXCPPSDKDISHCORE_EXPORT yxPermission
{
public:
    static std::string getCpuName();
    static std::string getDeviceInfo();
    static std::string getDeviceNo();
    static std::string getProductNo();
    static std::string getDeviceLicense(const std::string &key);

    static void setAppAuth(const std::string &appAuth);
    static bool activate(const std::string &licenseNo, std::string &error);
    static bool deactive(std::string &error);
    static bool checkLicense(bool bCheckCloud = false);
    static bool licenseInfo(QString &storeNo, QString &license);

private:
    static std::string m_key;
};
#endif

 
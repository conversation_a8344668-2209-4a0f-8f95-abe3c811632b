# ModelConverter 使用说明

## 快速开始

### 1. 环境诊断
首先运行诊断脚本检查环境：
```bash
chmod +x diagnose.sh
./diagnose.sh
```

### 2. 快速启动（推荐）
```bash
chmod +x quick_start.sh
./quick_start.sh
```

### 3. 完整启动脚本
```bash
chmod +x run_modelconverter.sh
./run_modelconverter.sh --gui
```

## 脚本说明

### diagnose.sh - 环境诊断
- 检查系统架构和编译工具
- 验证Qt库和路径配置
- 检查许可证库文件
- 分析X11环境设置
- 提供修复建议

### quick_start.sh - 快速启动
- 自动编译（如果需要）
- 自动设置环境变量
- 自动配置X11（WSL2环境）
- 直接启动程序

### run_modelconverter.sh - 完整启动
- 支持GUI和无界面模式
- 详细的环境检查
- 完整的错误处理
- 支持命令行参数

### test_build.sh - 编译测试
- 验证编译环境
- 执行完整编译
- 检查依赖库
- 生成详细报告

## 常见问题解决

### 1. 编译成功但找不到可执行文件

**问题**: 显示"编译成功"但ls找不到ModelConverter文件

**原因**: 可执行文件名称可能不同，或在子目录中

**解决**: 
```bash
# 查找可执行文件
find build -name "*[Mm]odel*" -type f -executable
# 或者查看build目录内容
ls -la build/
```

### 2. Qt库路径问题

**问题**: qmake失败或找不到Qt库

**解决**:
1. 检查Qt路径是否正确：
```bash
ls -la /opt/Qt5.12.6/5.12.6/gcc_64/
```

2. 如果路径不存在，修改`ModelConverter.pro`中的`QMAKE_PREFIX_PATH`

3. 或者使用系统Qt：
```bash
sudo apt install -y qtbase5-dev qt5-qmake qtbase5-dev-tools
```

### 3. 许可证库问题

**问题**: 链接时找不到libsf-core-ls

**解决**:
1. 确保`libsf-core-ls.so`在项目根目录
2. 检查库文件架构：
```bash
file libsf-core-ls.so
```
3. 应该显示x86-64架构

### 4. X11显示问题（WSL2环境）

**问题**: GUI程序无法显示

**解决**:
1. 在Windows中安装并启动VcXsrv X11服务器
2. 设置DISPLAY环境变量：
```bash
export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
```
3. 测试X11连接：
```bash
xeyes  # 如果安装了x11-apps
```

### 5. 依赖库缺失

**问题**: 运行时提示缺少动态库

**解决**:
1. 检查依赖：
```bash
ldd build/ModelConverter
```
2. 设置库路径：
```bash
export LD_LIBRARY_PATH="/opt/Qt5.12.6/5.12.6/gcc_64/lib:.:$LD_LIBRARY_PATH"
```

## 使用流程

### 开发测试模式
1. 启动程序
2. 勾选"调试模式"
3. 输入任意格式正确的激活码（如：TEST-1234-5678-ABCD）
4. 点击激活
5. 进入主界面进行模型转换

### 生产模式
1. 启动程序
2. 输入真实激活码
3. 程序自动获取硬件指纹
4. 连接服务器验证
5. 激活成功后使用

## 激活码测试
- 调试模式：任意格式正确的激活码
- 生产模式：E76G-JEQR-EQRA-T7ZW（示例）

## 功能特性

### 激活系统
- 🎨 美观的现代化界面
- 🔧 调试模式和生产模式
- 🔒 硬件绑定验证
- 📱 设备信息显示
- ⚡ 实时状态反馈

### 转换界面
- 📋 多标签页输出（日志/错误/结果）
- 📊 实时进度显示
- 🎛️ 丰富的转换选项
- 📁 结果文件管理
- 🚀 Docker集成转换

## 目录结构
```
ModelConverter/
├── ModelConverter.pro      # 项目配置
├── main.cpp               # 主程序
├── activationwindow.*     # 激活窗口
├── conversionwindow.*     # 转换窗口
├── LicenseManager.*       # 许可证管理
├── libsf-core-ls.so      # 许可证库
├── build/                # 编译输出
├── icons/                # 图标资源
├── diagnose.sh           # 环境诊断
├── quick_start.sh        # 快速启动
├── run_modelconverter.sh # 完整启动
├── test_build.sh         # 编译测试
└── README_USAGE.md       # 本文档
```

## 技术支持

如果遇到问题：
1. 首先运行`./diagnose.sh`检查环境
2. 查看编译日志：`cat build/build.log`
3. 检查依赖库：`ldd build/ModelConverter`
4. 验证配置文件：`cat ModelConverter.pro`

更多技术细节请参考源码注释和相关文档。

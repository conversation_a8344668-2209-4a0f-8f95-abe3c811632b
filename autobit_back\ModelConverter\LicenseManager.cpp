#include "LicenseManager.h"
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonArray>
#include <QSysInfo>
#include <QNetworkInterface>
#include <QProcess>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QTimer>
#include <QUuid>

// 静态常量定义
const QString LicenseManager::SETTINGS_GROUP = "License";
const QString LicenseManager::DEFAULT_SERVER_URL = "https://api.autobit.com/license";
const QString LicenseManager::DEFAULT_PRODUCT_ID = "ModelConverter_v1.0";
const int LicenseManager::DEFAULT_TIMEOUT = 30000; // 30秒

LicenseManager::LicenseManager(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_settings(new QSettings(this))
    , m_serverUrl(DEFAULT_SERVER_URL)
    , m_productId(DEFAULT_PRODUCT_ID)
    , m_timeout(DEFAULT_TIMEOUT)
    , m_debugMode(false)
{
    // 初始化调试设备列表
    m_debugDevices << "设备1 - 50bf70582c5ea2ac7502656f8cfb522e"
                   << "设备2 - 60cf80693d6fb3bd8613767f9dfc633f"
                   << "设备3 - 70df90704e7fc4ce9724878f0efd744g";
    
    // 加载本地许可证
    m_currentLicense = loadLicenseFromLocal();
    
    qDebug() << "[LicenseManager] 初始化完成";
}

LicenseManager::~LicenseManager()
{
    qDebug() << "[LicenseManager] 析构";
}

LicenseManager::DeviceInfo LicenseManager::getDeviceInfo()
{
    DeviceInfo info;
    info.cpuId = getCpuId();
    info.motherboardId = getMotherboardId();
    info.macAddress = getMacAddress();
    info.systemInfo = getSystemInfo();
    
    // 生成设备ID和硬件指纹
    QString combined = info.cpuId + info.motherboardId + info.macAddress;
    info.hardwareHash = hashString(combined);
    info.deviceId = hashString(info.hardwareHash + "AUTOBIT_SALT");
    
    return info;
}

QString LicenseManager::generateDeviceId()
{
    return getDeviceInfo().deviceId;
}

QString LicenseManager::generateHardwareFingerprint()
{
    return getDeviceInfo().hardwareHash;
}

void LicenseManager::activateLicense(const QString &licenseKey)
{
    qDebug() << "[LicenseManager] 开始激活许可证:" << licenseKey;
    
    if (licenseKey.isEmpty()) {
        emit activationFinished(InvalidLicense, "激活码不能为空");
        return;
    }
    
    if (m_debugMode) {
        // 调试模式激活
        ActivationResult result = performDebugActivation(licenseKey);
        QString message = (result == Success) ? "调试模式激活成功" : "调试模式激活失败";
        emit activationFinished(result, message);
        return;
    }
    
    // 获取设备信息
    DeviceInfo deviceInfo = getDeviceInfo();
    qDebug() << "[LicenseManager] 设备ID:" << deviceInfo.deviceId;
    
    // 发送激活请求
    sendActivationRequest(licenseKey, deviceInfo);
}

void LicenseManager::deactivateLicense()
{
    qDebug() << "[LicenseManager] 停用许可证";
    
    if (m_debugMode) {
        clearLocalLicense();
        m_currentLicense = LicenseInfo();
        emit licenseStatusChanged(false);
        return;
    }
    
    sendDeactivationRequest();
}

bool LicenseManager::isLicenseValid()
{
    if (m_debugMode) {
        return m_currentLicense.isValid;
    }
    
    // 检查本地许可证
    if (!m_currentLicense.isValid) {
        return false;
    }
    
    // 检查过期时间
    if (m_currentLicense.expireDate.isValid() && 
        m_currentLicense.expireDate < QDateTime::currentDateTime()) {
        qDebug() << "[LicenseManager] 许可证已过期";
        return false;
    }
    
    // 检查硬件绑定
    DeviceInfo currentDevice = getDeviceInfo();
    if (m_currentLicense.deviceId != currentDevice.deviceId) {
        qDebug() << "[LicenseManager] 硬件不匹配";
        return false;
    }
    
    return true;
}

LicenseManager::LicenseInfo LicenseManager::getCurrentLicense()
{
    return m_currentLicense;
}

void LicenseManager::setDebugMode(bool enabled)
{
    m_debugMode = enabled;
    qDebug() << "[LicenseManager] 调试模式:" << (enabled ? "启用" : "禁用");
}

bool LicenseManager::isDebugMode() const
{
    return m_debugMode;
}

void LicenseManager::addDebugDevice(const QString &deviceId, const QString &description)
{
    QString deviceEntry = QString("%1 - %2").arg(description, deviceId);
    if (!m_debugDevices.contains(deviceEntry)) {
        m_debugDevices.append(deviceEntry);
    }
}

QStringList LicenseManager::getDebugDevices() const
{
    return m_debugDevices;
}

void LicenseManager::setServerUrl(const QString &url)
{
    m_serverUrl = url;
}

void LicenseManager::setProductId(const QString &productId)
{
    m_productId = productId;
}

void LicenseManager::setTimeout(int seconds)
{
    m_timeout = seconds * 1000;
}

void LicenseManager::sendActivationRequest(const QString &licenseKey, const DeviceInfo &deviceInfo)
{
    QUrl url(m_serverUrl + "/activate");
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    QJsonObject json;
    json["licenseKey"] = licenseKey;
    json["productId"] = m_productId;
    json["deviceInfo"] = deviceInfoToJson(deviceInfo);
    json["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(json);
    QByteArray data = doc.toJson();
    
    QNetworkReply *reply = m_networkManager->post(request, data);
    reply->setProperty("type", "activation");
    connect(reply, &QNetworkReply::finished, this, &LicenseManager::onActivationReplyFinished);
    
    // 设置超时
    QTimer::singleShot(m_timeout, reply, [reply]() {
        if (reply->isRunning()) {
            reply->abort();
        }
    });
    
    qDebug() << "[LicenseManager] 发送激活请求到:" << url.toString();
}

void LicenseManager::sendDeactivationRequest()
{
    if (!m_currentLicense.isValid) {
        return;
    }
    
    QUrl url(m_serverUrl + "/deactivate");
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    QJsonObject json;
    json["licenseKey"] = m_currentLicense.licenseKey;
    json["productId"] = m_productId;
    json["deviceId"] = m_currentLicense.deviceId;
    
    QJsonDocument doc(json);
    QByteArray data = doc.toJson();
    
    QNetworkReply *reply = m_networkManager->post(request, data);
    reply->setProperty("type", "deactivation");
    connect(reply, &QNetworkReply::finished, this, &LicenseManager::onDeactivationReplyFinished);
    
    qDebug() << "[LicenseManager] 发送停用请求";
}

void LicenseManager::sendValidationRequest()
{
    if (!m_currentLicense.isValid) {
        return;
    }
    
    QUrl url(m_serverUrl + "/validate");
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    QJsonObject json;
    json["licenseKey"] = m_currentLicense.licenseKey;
    json["productId"] = m_productId;
    json["deviceId"] = m_currentLicense.deviceId;
    
    QJsonDocument doc(json);
    QByteArray data = doc.toJson();
    
    QNetworkReply *reply = m_networkManager->post(request, data);
    reply->setProperty("type", "validation");
    connect(reply, &QNetworkReply::finished, this, &LicenseManager::onValidationReplyFinished);
    
    qDebug() << "[LicenseManager] 发送验证请求";
}

LicenseManager::ActivationResult LicenseManager::performDebugActivation(const QString &licenseKey)
{
    qDebug() << "[LicenseManager] 调试模式激活:" << licenseKey;
    
    // 简单的格式验证
    if (licenseKey.length() < 10 || !licenseKey.contains("-")) {
        return InvalidLicense;
    }
    
    // 创建调试许可证
    LicenseInfo debugLicense;
    debugLicense.licenseKey = licenseKey;
    debugLicense.deviceId = generateDeviceId();
    debugLicense.productId = m_productId;
    debugLicense.userId = "DEBUG_USER";
    debugLicense.issueDate = QDateTime::currentDateTime();
    debugLicense.expireDate = QDateTime::currentDateTime().addYears(1);
    debugLicense.maxActivations = 999;
    debugLicense.currentActivations = 1;
    debugLicense.isValid = true;
    
    m_currentLicense = debugLicense;
    saveLicenseToLocal(debugLicense);
    
    emit licenseStatusChanged(true);
    return Success;
}

void LicenseManager::onActivationReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    reply->deleteLater();

    if (reply->error() != QNetworkReply::NoError) {
        qDebug() << "[LicenseManager] 网络错误:" << reply->errorString();
        emit activationFinished(NetworkError, "网络连接失败: " + reply->errorString());
        return;
    }

    QByteArray data = reply->readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        qDebug() << "[LicenseManager] JSON解析错误:" << error.errorString();
        emit activationFinished(ServerError, "服务器响应格式错误");
        return;
    }

    QJsonObject response = doc.object();
    bool success = response["success"].toBool();
    QString message = response["message"].toString();

    if (success) {
        QJsonObject licenseData = response["license"].toObject();
        LicenseInfo license = licenseInfoFromJson(licenseData);

        m_currentLicense = license;
        saveLicenseToLocal(license);

        emit licenseStatusChanged(true);
        emit activationFinished(Success, message);
        qDebug() << "[LicenseManager] 激活成功";
    } else {
        QString errorCode = response["errorCode"].toString();
        ActivationResult result = InvalidLicense;

        if (errorCode == "HARDWARE_MISMATCH") {
            result = HardwareMismatch;
        } else if (errorCode == "LICENSE_EXPIRED") {
            result = LicenseExpired;
        } else if (errorCode == "LICENSE_EXHAUSTED") {
            result = LicenseExhausted;
        }

        emit activationFinished(result, message);
        qDebug() << "[LicenseManager] 激活失败:" << message;
    }
}

void LicenseManager::onDeactivationReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    reply->deleteLater();

    // 无论服务器响应如何，都清除本地许可证
    clearLocalLicense();
    m_currentLicense = LicenseInfo();
    emit licenseStatusChanged(false);

    qDebug() << "[LicenseManager] 许可证已停用";
}

void LicenseManager::onValidationReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    reply->deleteLater();

    if (reply->error() != QNetworkReply::NoError) {
        qDebug() << "[LicenseManager] 验证网络错误:" << reply->errorString();
        return;
    }

    QByteArray data = reply->readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    QJsonObject response = doc.object();

    bool isValid = response["valid"].toBool();
    if (!isValid) {
        qDebug() << "[LicenseManager] 服务器验证失败，清除本地许可证";
        clearLocalLicense();
        m_currentLicense = LicenseInfo();
        emit licenseStatusChanged(false);
    }
}

void LicenseManager::saveLicenseToLocal(const LicenseInfo &license)
{
    m_settings->beginGroup(SETTINGS_GROUP);

    QJsonObject json = licenseInfoToJson(license);
    QJsonDocument doc(json);
    QString licenseData = QString::fromUtf8(doc.toJson());

    // 简单加密存储
    QString encryptedData = hashString(licenseData + "AUTOBIT_ENCRYPT_KEY");
    m_settings->setValue("licenseData", licenseData);
    m_settings->setValue("checksum", encryptedData);

    m_settings->endGroup();
    qDebug() << "[LicenseManager] 许可证已保存到本地";
}

LicenseManager::LicenseInfo LicenseManager::loadLicenseFromLocal()
{
    LicenseInfo license;

    m_settings->beginGroup(SETTINGS_GROUP);

    QString licenseData = m_settings->value("licenseData").toString();
    QString checksum = m_settings->value("checksum").toString();

    m_settings->endGroup();

    if (licenseData.isEmpty() || checksum.isEmpty()) {
        return license; // 返回无效许可证
    }

    // 验证校验和
    QString expectedChecksum = hashString(licenseData + "AUTOBIT_ENCRYPT_KEY");
    if (checksum != expectedChecksum) {
        qDebug() << "[LicenseManager] 本地许可证校验失败";
        clearLocalLicense();
        return license;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(licenseData.toUtf8(), &error);
    if (error.error != QJsonParseError::NoError) {
        qDebug() << "[LicenseManager] 本地许可证解析失败";
        clearLocalLicense();
        return license;
    }

    license = licenseInfoFromJson(doc.object());
    qDebug() << "[LicenseManager] 从本地加载许可证";

    return license;
}

void LicenseManager::clearLocalLicense()
{
    m_settings->beginGroup(SETTINGS_GROUP);
    m_settings->remove("licenseData");
    m_settings->remove("checksum");
    m_settings->endGroup();

    qDebug() << "[LicenseManager] 本地许可证已清除";
}

QString LicenseManager::getCpuId()
{
    QString cpuId;

#ifdef Q_OS_WIN
    QProcess process;
    process.start("wmic", QStringList() << "cpu" << "get" << "ProcessorId" << "/value");
    process.waitForFinished();
    QString output = process.readAllStandardOutput();

    QStringList lines = output.split('\n');
    for (const QString &line : lines) {
        if (line.startsWith("ProcessorId=")) {
            cpuId = line.mid(12).trimmed();
            break;
        }
    }
#elif defined(Q_OS_LINUX)
    QProcess process;
    process.start("cat", QStringList() << "/proc/cpuinfo");
    process.waitForFinished();
    QString output = process.readAllStandardOutput();

    QStringList lines = output.split('\n');
    for (const QString &line : lines) {
        if (line.startsWith("processor")) {
            QStringList parts = line.split(':');
            if (parts.size() > 1) {
                cpuId = parts[1].trimmed();
                break;
            }
        }
    }
#endif

    if (cpuId.isEmpty()) {
        cpuId = QSysInfo::currentCpuArchitecture() + "_" + QSysInfo::buildCpuArchitecture();
    }

    return cpuId;
}

QString LicenseManager::getMotherboardId()
{
    QString motherboardId;

#ifdef Q_OS_WIN
    QProcess process;
    process.start("wmic", QStringList() << "baseboard" << "get" << "SerialNumber" << "/value");
    process.waitForFinished();
    QString output = process.readAllStandardOutput();

    QStringList lines = output.split('\n');
    for (const QString &line : lines) {
        if (line.startsWith("SerialNumber=")) {
            motherboardId = line.mid(13).trimmed();
            break;
        }
    }
#elif defined(Q_OS_LINUX)
    QProcess process;
    process.start("sudo", QStringList() << "dmidecode" << "-s" << "baseboard-serial-number");
    process.waitForFinished();
    motherboardId = process.readAllStandardOutput().trimmed();

    if (motherboardId.isEmpty()) {
        // 备用方法
        process.start("cat", QStringList() << "/sys/class/dmi/id/board_serial");
        process.waitForFinished();
        motherboardId = process.readAllStandardOutput().trimmed();
    }
#endif

    if (motherboardId.isEmpty()) {
        motherboardId = QSysInfo::machineHostName();
    }

    return motherboardId;
}

QString LicenseManager::getMacAddress()
{
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();

    for (const QNetworkInterface &interface : interfaces) {
        if (interface.flags().testFlag(QNetworkInterface::IsUp) &&
            interface.flags().testFlag(QNetworkInterface::IsRunning) &&
            !interface.flags().testFlag(QNetworkInterface::IsLoopBack)) {

            QString mac = interface.hardwareAddress();
            if (!mac.isEmpty() && mac != "00:00:00:00:00:00") {
                return mac;
            }
        }
    }

    return "UNKNOWN_MAC";
}

QString LicenseManager::getSystemInfo()
{
    return QString("%1_%2_%3")
           .arg(QSysInfo::productType())
           .arg(QSysInfo::productVersion())
           .arg(QSysInfo::currentCpuArchitecture());
}

QString LicenseManager::hashString(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QJsonObject LicenseManager::deviceInfoToJson(const DeviceInfo &info)
{
    QJsonObject json;
    json["deviceId"] = info.deviceId;
    json["cpuId"] = info.cpuId;
    json["motherboardId"] = info.motherboardId;
    json["macAddress"] = info.macAddress;
    json["systemInfo"] = info.systemInfo;
    json["hardwareHash"] = info.hardwareHash;
    return json;
}

LicenseManager::DeviceInfo LicenseManager::deviceInfoFromJson(const QJsonObject &json)
{
    DeviceInfo info;
    info.deviceId = json["deviceId"].toString();
    info.cpuId = json["cpuId"].toString();
    info.motherboardId = json["motherboardId"].toString();
    info.macAddress = json["macAddress"].toString();
    info.systemInfo = json["systemInfo"].toString();
    info.hardwareHash = json["hardwareHash"].toString();
    return info;
}

QJsonObject LicenseManager::licenseInfoToJson(const LicenseInfo &info)
{
    QJsonObject json;
    json["licenseKey"] = info.licenseKey;
    json["deviceId"] = info.deviceId;
    json["productId"] = info.productId;
    json["userId"] = info.userId;
    json["issueDate"] = info.issueDate.toString(Qt::ISODate);
    json["expireDate"] = info.expireDate.toString(Qt::ISODate);
    json["maxActivations"] = info.maxActivations;
    json["currentActivations"] = info.currentActivations;
    json["isValid"] = info.isValid;
    return json;
}

LicenseManager::LicenseInfo LicenseManager::licenseInfoFromJson(const QJsonObject &json)
{
    LicenseInfo info;
    info.licenseKey = json["licenseKey"].toString();
    info.deviceId = json["deviceId"].toString();
    info.productId = json["productId"].toString();
    info.userId = json["userId"].toString();
    info.issueDate = QDateTime::fromString(json["issueDate"].toString(), Qt::ISODate);
    info.expireDate = QDateTime::fromString(json["expireDate"].toString(), Qt::ISODate);
    info.maxActivations = json["maxActivations"].toInt();
    info.currentActivations = json["currentActivations"].toInt();
    info.isValid = json["isValid"].toBool();
    return info;
}

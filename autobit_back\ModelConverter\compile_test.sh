#!/bin/bash
# 简单的编译测试脚本

echo "=== ModelConverter 编译测试 ==="

# 进入项目目录
cd "$(dirname "$0")"

# 检查关键文件
echo "1. 检查项目文件:"
for file in ModelConverter.pro main.cpp activationwindow.cpp conversionwindow.cpp YXPermission.cpp LicenseManager.cpp; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file 缺失"
    fi
done

# 检查许可证库
echo ""
echo "2. 检查许可证库:"
if [ -f "libsf-core-ls.so" ]; then
    echo "   ✅ libsf-core-ls.so 存在"
    echo "   架构: $(file libsf-core-ls.so | cut -d: -f2)"
else
    echo "   ❌ libsf-core-ls.so 缺失"
fi

# 检查Qt环境
echo ""
echo "3. 检查Qt环境:"
echo "   qmake版本: $(qmake --version | grep Qt)"
echo "   Qt路径: /opt/Qt5.12.6/5.12.6/gcc_64"
if [ -d "/opt/Qt5.12.6/5.12.6/gcc_64" ]; then
    echo "   ✅ Qt路径存在"
else
    echo "   ❌ Qt路径不存在"
fi

# 清理并编译
echo ""
echo "4. 开始编译:"
rm -rf build
mkdir -p build
cd build

# 复制许可证库
if [ -f "../libsf-core-ls.so" ]; then
    cp ../libsf-core-ls.so .
fi

# qmake
echo "   执行qmake..."
qmake ../ModelConverter.pro > qmake.log 2>&1

if [ $? -eq 0 ]; then
    echo "   ✅ qmake成功"
    
    # make
    echo "   执行make..."
    make > make.log 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   🎉 编译成功!"
        
        # 查找可执行文件
        if [ -f "ModelConverter" ]; then
            echo "   ✅ 可执行文件: ModelConverter"
            echo "   文件信息: $(file ModelConverter)"
            echo "   大小: $(ls -lh ModelConverter | awk '{print $5}')"
        else
            echo "   ❌ 未找到ModelConverter可执行文件"
            echo "   build目录内容:"
            ls -la
        fi
        
    else
        echo "   ❌ make失败"
        echo "   错误信息:"
        tail -10 make.log
    fi
    
else
    echo "   ❌ qmake失败"
    echo "   错误信息:"
    cat qmake.log
fi

echo ""
echo "=== 编译测试完成 ==="

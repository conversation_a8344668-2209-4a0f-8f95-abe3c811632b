#include "conversionwindow.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QFileDialog>
#include <QDesktopServices>
#include <QDir>
#include <QCoreApplication>
#include <QMessageBox>
#include <QFileInfo>
#include <QDateTime>
#include <QApplication>
#include <QScreen>
#include <QSplitter>

ConversionWindow::ConversionWindow(QWidget *parent)
    : QWidget(parent), process(nullptr), progressValue(0)
{
    setupUI();
    setupStyles();
    setupConnections();

    // 居中显示窗口
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);

    // 初始化进度定时器
    progressTimer = new QTimer(this);
    connect(progressTimer, &QTimer::timeout, [this]() {
        progressValue = (progressValue + 1) % 101;
        updateProgress(progressValue);
    });
}

void ConversionWindow::setupUI()
{
    setWindowTitle("模型转换器 - 主界面");
    setMinimumSize(900, 700);
    resize(1200, 800);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 标题栏
    headerFrame = new QFrame(this);
    headerFrame->setObjectName("headerFrame");
    headerFrame->setFixedHeight(80);

    titleLabel = new QLabel("🤖 AI模型转换器", this);
    titleLabel->setObjectName("titleLabel");

    statusIconLabel = new QLabel("⚪", this);
    statusIconLabel->setObjectName("statusIcon");

    QHBoxLayout *headerLayout = new QHBoxLayout(headerFrame);
    headerLayout->addWidget(titleLabel);
    headerLayout->addStretch();
    headerLayout->addWidget(statusIconLabel);

    // 输入区域
    inputFrame = new QFrame(this);
    inputFrame->setObjectName("inputFrame");

    // 模型选择组
    modelGroup = new QGroupBox("模型配置", this);
    modelGroup->setObjectName("modelGroup");

    modelTypeCombo = new QComboBox(this);
    modelTypeCombo->setObjectName("modelTypeCombo");
    modelTypeCombo->addItems({"yolov5", "yolov8", "yolov10"});

    modelPathEdit = new QLineEdit(this);
    modelPathEdit->setObjectName("modelPathEdit");
    modelPathEdit->setPlaceholderText("选择模型文件 (.pt, .onnx)");

    browseButton = new QPushButton("📁 浏览", this);
    browseButton->setObjectName("browseButton");

    QGridLayout *modelLayout = new QGridLayout(modelGroup);
    modelLayout->addWidget(new QLabel("模型类型:"), 0, 0);
    modelLayout->addWidget(modelTypeCombo, 0, 1);
    modelLayout->addWidget(new QLabel("模型文件:"), 1, 0);
    modelLayout->addWidget(modelPathEdit, 1, 1);
    modelLayout->addWidget(browseButton, 1, 2);

    // 选项组
    optionsGroup = new QGroupBox("转换选项", this);
    optionsGroup->setObjectName("optionsGroup");

    outputFormatCombo = new QComboBox(this);
    outputFormatCombo->setObjectName("outputFormatCombo");
    outputFormatCombo->addItems({"RKNN", "ONNX", "TensorRT"});

    quantizationCombo = new QComboBox(this);
    quantizationCombo->setObjectName("quantizationCombo");
    quantizationCombo->addItems({"INT8", "FP16", "FP32"});

    QGridLayout *optionsLayout = new QGridLayout(optionsGroup);
    optionsLayout->addWidget(new QLabel("输出格式:"), 0, 0);
    optionsLayout->addWidget(outputFormatCombo, 0, 1);
    optionsLayout->addWidget(new QLabel("量化精度:"), 0, 2);
    optionsLayout->addWidget(quantizationCombo, 0, 3);

    // 操作组
    actionGroup = new QGroupBox("操作", this);
    actionGroup->setObjectName("actionGroup");

    convertButton = new QPushButton("🚀 开始转换", this);
    convertButton->setObjectName("convertButton");
    convertButton->setDefault(true);

    clearLogButton = new QPushButton("🗑️ 清空日志", this);
    clearLogButton->setObjectName("clearLogButton");

    openResultButton = new QPushButton("📂 打开结果", this);
    openResultButton->setObjectName("openResultButton");
    openResultButton->setEnabled(false);

    QHBoxLayout *actionLayout = new QHBoxLayout(actionGroup);
    actionLayout->addWidget(convertButton);
    actionLayout->addWidget(clearLogButton);
    actionLayout->addWidget(openResultButton);
    actionLayout->addStretch();

    // 进度条
    progressBar = new QProgressBar(this);
    progressBar->setObjectName("progressBar");
    progressBar->setVisible(false);

    progressLabel = new QLabel("准备就绪", this);
    progressLabel->setObjectName("progressLabel");

    // 输入区域布局
    QVBoxLayout *inputLayout = new QVBoxLayout(inputFrame);
    inputLayout->addWidget(modelGroup);
    inputLayout->addWidget(optionsGroup);
    inputLayout->addWidget(actionGroup);
    inputLayout->addWidget(progressLabel);
    inputLayout->addWidget(progressBar);

    // 输出区域
    outputFrame = new QFrame(this);
    outputFrame->setObjectName("outputFrame");

    outputTabs = new QTabWidget(this);
    outputTabs->setObjectName("outputTabs");

    // 日志标签页
    logDisplay = new QTextEdit(this);
    logDisplay->setObjectName("logDisplay");
    logDisplay->setReadOnly(true);
    outputTabs->addTab(logDisplay, "📋 转换日志");

    // 错误标签页
    errorDisplay = new QTextEdit(this);
    errorDisplay->setObjectName("errorDisplay");
    errorDisplay->setReadOnly(true);
    outputTabs->addTab(errorDisplay, "⚠️ 错误信息");

    // 结果标签页
    resultList = new QListWidget(this);
    resultList->setObjectName("resultList");
    outputTabs->addTab(resultList, "📁 转换结果");

    QVBoxLayout *outputLayout = new QVBoxLayout(outputFrame);
    outputLayout->addWidget(outputTabs);

    // 状态栏
    statusLabel = new QLabel("准备就绪 - 请选择模型文件开始转换", this);
    statusLabel->setObjectName("statusLabel");

    // 使用分割器
    QSplitter *splitter = new QSplitter(Qt::Vertical, this);
    splitter->addWidget(inputFrame);
    splitter->addWidget(outputFrame);
    splitter->setStretchFactor(0, 0);
    splitter->setStretchFactor(1, 1);
    splitter->setSizes({300, 400});

    // 主布局
    mainLayout->addWidget(headerFrame);
    mainLayout->addWidget(splitter);
    mainLayout->addWidget(statusLabel);
}

void ConversionWindow::setupStyles()
{
    setStyleSheet(R"(
        QWidget {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background-color: #f8f9fa;
        }

        #headerFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #007bff, stop:1 #0056b3);
            border: none;
        }

        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: white;
            padding: 20px;
        }

        #statusIcon {
            font-size: 20px;
            padding: 20px;
        }

        #inputFrame {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 10px;
        }

        #outputFrame {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 10px;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #495057;
        }

        QComboBox, QLineEdit {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }

        QComboBox:focus, QLineEdit:focus {
            border-color: #007bff;
        }

        QPushButton {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            background-color: #6c757d;
            color: white;
        }

        QPushButton:hover {
            background-color: #5a6268;
        }

        #convertButton {
            background-color: #28a745;
            font-size: 16px;
            padding: 12px 24px;
        }

        #convertButton:hover {
            background-color: #218838;
        }

        #convertButton:disabled {
            background-color: #6c757d;
        }

        #browseButton {
            background-color: #007bff;
        }

        #browseButton:hover {
            background-color: #0056b3;
        }

        #clearLogButton {
            background-color: #ffc107;
            color: #212529;
        }

        #clearLogButton:hover {
            background-color: #e0a800;
        }

        #openResultButton {
            background-color: #17a2b8;
        }

        #openResultButton:hover {
            background-color: #138496;
        }

        QProgressBar {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        QProgressBar::chunk {
            background-color: #007bff;
            border-radius: 6px;
        }

        QTabWidget::pane {
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }

        QTabBar::tab {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 8px 16px;
            margin-right: 2px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }

        QTextEdit, QListWidget {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background-color: white;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }

        #statusLabel {
            background-color: #e9ecef;
            padding: 8px 16px;
            border-top: 1px solid #dee2e6;
            font-size: 13px;
            color: #495057;
        }

        #progressLabel {
            font-size: 13px;
            color: #6c757d;
            padding: 5px;
        }
    )");
}

void ConversionWindow::setupConnections()
{
    connect(browseButton, &QPushButton::clicked, this, &ConversionWindow::onBrowseClicked);
    connect(convertButton, &QPushButton::clicked, this, &ConversionWindow::onConvertClicked);
    connect(clearLogButton, &QPushButton::clicked, this, &ConversionWindow::onClearLogClicked);
    connect(openResultButton, &QPushButton::clicked, this, &ConversionWindow::onOpenResultClicked);
}

void ConversionWindow::updateProgress(int value)
{
    progressBar->setValue(value);
    if (value < 100) {
        progressLabel->setText(QString("转换进度: %1%").arg(value));
    } else {
        progressLabel->setText("转换完成");
    }
}

void ConversionWindow::setConversionStatus(const QString &status, const QString &type)
{
    statusLabel->setText(status);

    if (type == "success") {
        statusIconLabel->setText("✅");
        statusLabel->setStyleSheet("color: #28a745;");
    } else if (type == "error") {
        statusIconLabel->setText("❌");
        statusLabel->setStyleSheet("color: #dc3545;");
    } else if (type == "working") {
        statusIconLabel->setText("⚙️");
        statusLabel->setStyleSheet("color: #007bff;");
    } else {
        statusIconLabel->setText("⚪");
        statusLabel->setStyleSheet("color: #495057;");
    }
}

void ConversionWindow::addLogMessage(const QString &message, const QString &type)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString formattedMessage;

    if (type == "error") {
        formattedMessage = QString("[%1] ❌ %2").arg(timestamp, message);
        errorDisplay->append(formattedMessage);
    } else if (type == "success") {
        formattedMessage = QString("[%1] ✅ %2").arg(timestamp, message);
    } else if (type == "warning") {
        formattedMessage = QString("[%1] ⚠️ %2").arg(timestamp, message);
    } else {
        formattedMessage = QString("[%1] ℹ️ %2").arg(timestamp, message);
    }

    logDisplay->append(formattedMessage);

    // 自动滚动到底部
    QTextCursor cursor = logDisplay->textCursor();
    cursor.movePosition(QTextCursor::End);
    logDisplay->setTextCursor(cursor);
}

void ConversionWindow::resetUI()
{
    progressBar->setVisible(false);
    progressBar->setValue(0);
    progressValue = 0;
    progressTimer->stop();

    convertButton->setEnabled(true);
    convertButton->setText("🚀 开始转换");

    setConversionStatus("准备就绪 - 请选择模型文件开始转换", "info");
}

void ConversionWindow::onClearLogClicked()
{
    logDisplay->clear();
    errorDisplay->clear();
    resultList->clear();
    addLogMessage("日志已清空", "info");
}

void ConversionWindow::onOpenResultClicked()
{
    QString buildDir = QCoreApplication::applicationDirPath();
    QString resultPath = buildDir + "/result";

    if (QDir(resultPath).exists()) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(resultPath));
        addLogMessage("已打开结果目录: " + resultPath, "info");
    } else {
        QMessageBox::warning(this, "警告", "结果目录不存在: " + resultPath);
        addLogMessage("结果目录不存在: " + resultPath, "error");
    }
}

void ConversionWindow::onBrowseClicked()
{
    QString filePath = QFileDialog::getOpenFileName(
        this,
        "选择模型文件",
        QDir::homePath(),
        "模型文件 (*.pt *.onnx *.pth);;PyTorch模型 (*.pt *.pth);;ONNX模型 (*.onnx);;所有文件 (*.*)"
    );

    if (!filePath.isEmpty()) {
        modelPathEdit->setText(filePath);

        // 自动检测模型类型
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName().toLower();

        if (fileName.contains("yolov5")) {
            modelTypeCombo->setCurrentText("yolov5");
        } else if (fileName.contains("yolov8")) {
            modelTypeCombo->setCurrentText("yolov8");
        } else if (fileName.contains("yolov10")) {
            modelTypeCombo->setCurrentText("yolov10");
        }

        addLogMessage("已选择模型文件: " + filePath, "info");
        setConversionStatus("模型文件已选择，可以开始转换", "info");
    }
}

void ConversionWindow::onConvertClicked()
{
    QString modelPath = modelPathEdit->text().trimmed();
    if (modelPath.isEmpty()) {
        addLogMessage("请先选择模型文件", "error");
        QMessageBox::warning(this, "错误", "请先选择模型文件！");
        return;
    }

    if (!QFile::exists(modelPath)) {
        addLogMessage("模型文件不存在: " + modelPath, "error");
        QMessageBox::warning(this, "错误", "选择的模型文件不存在！");
        return;
    }

    // 开始转换
    convertButton->setEnabled(false);
    convertButton->setText("⏳ 转换中...");
    progressBar->setVisible(true);
    progressTimer->start(100);

    setConversionStatus("正在准备转换环境...", "working");
    addLogMessage("开始模型转换", "info");

    QString modelType = modelTypeCombo->currentText();
    QString outputFormat = outputFormatCombo->currentText();
    QString quantization = quantizationCombo->currentText();
    QString fileName = QFileInfo(modelPath).fileName();

    addLogMessage(QString("模型类型: %1").arg(modelType), "info");
    addLogMessage(QString("输出格式: %1").arg(outputFormat), "info");
    addLogMessage(QString("量化精度: %1").arg(quantization), "info");
    addLogMessage(QString("源文件: %1").arg(modelPath), "info");

    // 确保目录结构存在
    QString buildDir = QCoreApplication::applicationDirPath();
    QString modelsDir = buildDir + "/models";
    QString resultDir = buildDir + "/result";

    QDir().mkpath(modelsDir);
    QDir().mkpath(resultDir);

    // 复制模型文件
    QString destinationModelPath = modelsDir + "/" + fileName;
    if (QFile::exists(destinationModelPath)) {
        QFile::remove(destinationModelPath);
    }

    if (!QFile::copy(modelPath, destinationModelPath)) {
        addLogMessage("无法复制模型文件到: " + destinationModelPath, "error");
        resetUI();
        QMessageBox::critical(this, "错误", "无法复制模型文件！");
        return;
    }

    addLogMessage("模型文件已复制到: " + destinationModelPath, "success");
    setConversionStatus("正在启动Docker容器...", "working");

    // 构造Docker命令
    QString dockerModelPath = "/workspace/yolo-rk3588/models";
    QString dockerOutputPath = "result";

    QString dockerCommand = QString(
        "docker run --rm "
        "-v \"%1\":%2 "
        "-v \"%3\":/workspace/yolo-rk3588/result "
        "-w /workspace/yolo-rk3588 "
        "model-converter:latest "
        "--model %4 --weight %5 --output %6 --format %7 --quantization %8")
        .arg(modelsDir)
        .arg(dockerModelPath)
        .arg(resultDir)
        .arg(modelType)
        .arg(dockerModelPath + "/" + fileName)
        .arg(dockerOutputPath)
        .arg(outputFormat.toLower())
        .arg(quantization.toLower());

    addLogMessage("Docker命令: " + dockerCommand, "info");

    // 启动转换进程
    if (process) {
        process->deleteLater();
    }

    process = new QProcess(this);
    connect(process, &QProcess::readyReadStandardOutput, this, &ConversionWindow::onProcessOutput);
    connect(process, &QProcess::readyReadStandardError, this, &ConversionWindow::onProcessErrorOutput);
    connect(process, QOverload<int>::of(&QProcess::finished), this, &ConversionWindow::onProcessFinished);

    setConversionStatus("正在执行模型转换...", "working");
    process->start("bash", QStringList() << "-c" << dockerCommand);

    if (!process->waitForStarted(5000)) {
        addLogMessage("无法启动转换进程", "error");
        resetUI();
        QMessageBox::critical(this, "错误", "无法启动转换进程！请检查Docker是否正常运行。");
    }
}


void ConversionWindow::onProcessFinished(int exitCode)
{
    progressTimer->stop();
    progressBar->setValue(100);

    QString buildDir = QCoreApplication::applicationDirPath();
    QString resultPath = buildDir + "/result";

    if (exitCode == 0) {
        setConversionStatus("✅ 转换成功完成！", "success");
        addLogMessage("模型转换成功完成", "success");
        addLogMessage("结果保存路径: " + resultPath, "info");

        // 扫描结果文件
        QDir resultDir(resultPath);
        QStringList filters;
        filters << "*.rknn" << "*.onnx" << "*.trt" << "*.engine";
        QStringList resultFiles = resultDir.entryList(filters, QDir::Files);

        resultList->clear();
        for (const QString &file : resultFiles) {
            QListWidgetItem *item = new QListWidgetItem("📄 " + file);
            item->setData(Qt::UserRole, resultDir.absoluteFilePath(file));
            resultList->addItem(item);
        }

        if (!resultFiles.isEmpty()) {
            outputTabs->setCurrentIndex(2); // 切换到结果标签页
            openResultButton->setEnabled(true);
        }

        // 显示成功消息
        QMessageBox::information(this, "转换完成",
            QString("模型转换成功完成！\n\n生成了 %1 个结果文件\n结果保存在: %2")
            .arg(resultFiles.count()).arg(resultPath));

    } else {
        setConversionStatus("❌ 转换失败", "error");
        addLogMessage(QString("转换进程异常退出，退出码: %1").arg(exitCode), "error");

        outputTabs->setCurrentIndex(1); // 切换到错误标签页

        QMessageBox::critical(this, "转换失败",
            QString("模型转换失败！\n\n退出码: %1\n请查看错误信息标签页获取详细信息。").arg(exitCode));
    }

    resetUI();

    if (process) {
        process->deleteLater();
        process = nullptr;
    }
}

void ConversionWindow::onProcessOutput()
{
    if (!process) return;

    QByteArray output = process->readAllStandardOutput();
    QString outputText = QString::fromLocal8Bit(output);

    // 解析输出，提取有用信息
    QStringList lines = outputText.split('\n', QString::SkipEmptyParts);
    for (const QString &line : lines) {
        QString trimmedLine = line.trimmed();
        if (!trimmedLine.isEmpty()) {
            if (trimmedLine.contains("error", Qt::CaseInsensitive) ||
                trimmedLine.contains("failed", Qt::CaseInsensitive)) {
                addLogMessage(trimmedLine, "error");
            } else if (trimmedLine.contains("warning", Qt::CaseInsensitive)) {
                addLogMessage(trimmedLine, "warning");
            } else if (trimmedLine.contains("success", Qt::CaseInsensitive) ||
                       trimmedLine.contains("complete", Qt::CaseInsensitive)) {
                addLogMessage(trimmedLine, "success");
            } else {
                addLogMessage(trimmedLine, "info");
            }
        }
    }
}

void ConversionWindow::onProcessErrorOutput()
{
    if (!process) return;

    QByteArray errorOutput = process->readAllStandardError();
    QString errorText = QString::fromLocal8Bit(errorOutput);

    QStringList lines = errorText.split('\n', QString::SkipEmptyParts);
    for (const QString &line : lines) {
        QString trimmedLine = line.trimmed();
        if (!trimmedLine.isEmpty()) {
            addLogMessage(trimmedLine, "error");
        }
    }
}







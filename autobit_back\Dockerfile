FROM docker-0.unsee.tech/library/ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive

# 1. 系统依赖
RUN apt-get update && apt-get install -y \
    cmake \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    git curl wget \
    wget curl ca-certificates git \
    python3 python3-pip \
    && rm -rf /var/lib/apt/lists/*

# 2. 安装 Miniconda
ENV CONDA_DIR=/opt/conda
RUN wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh \
    && bash /tmp/miniconda.sh -b -p "$CONDA_DIR" \
    && rm /tmp/miniconda.sh \
    && "$CONDA_DIR/bin/conda" clean --all -y
ENV PATH="$CONDA_DIR/bin:$PATH"

# 3. 配置国内镜像源（清华）
RUN mkdir -p /root/.conda && \
    echo "channels:\n  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main\n  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free\n  - defaults\nshow_channel_urls: true" > /root/.condarc
RUN mkdir -p /etc && \
    echo "[global]\nindex-url = https://pypi.tuna.tsinghua.edu.cn/simple\ntimeout = 60" > /etc/pip.conf

# 4. 复制 yolo-rk3588 目录并创建环境
WORKDIR /workspace/yolo-rk3588
COPY yolo-rk3588/ ./
# 删除无效依赖（panopticapi、grpcio）并创建环境
RUN sed -i '/panopticapi==0.1/d; /grpcio==1.70.0/d' rknn_env.yml \
    && conda env create -f rknn_env.yml

# 5. 切换至 RKNN 环境
SHELL ["conda", "run", "-n", "RKNN-Toolkit2", "/bin/bash", "-lc"]

# 6. 安装额外 Python 依赖（requirements.txt 和 rknn-toolkit2）
RUN if [ -f requirements.txt ]; then \
        pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple; \
    fi \
    && pip install rknn-toolkit2 -i https://pypi.tuna.tsinghua.edu.cn/simple


# 7. 设置默认工作路径与入口
WORKDIR /workspace/yolo-rk3588
ENTRYPOINT ["bash", "entrypoint.sh"]

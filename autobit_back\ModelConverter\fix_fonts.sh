#!/bin/bash
# 字体修复脚本 - 解决中文字体显示问题

echo "=== 修复字体显示问题 ==="

# 1. 检查系统字体
echo "1. 检查系统字体..."
echo "   可用的中文字体:"
fc-list :lang=zh | head -5

# 2. 安装中文字体（如果需要）
echo ""
echo "2. 检查并安装中文字体..."
if ! fc-list | grep -q "SimHei\|Microsoft YaHei\|WenQuanYi"; then
    echo "   正在安装中文字体..."
    sudo apt update
    sudo apt install -y fonts-wqy-microhei fonts-wqy-zenhei fonts-arphic-ukai fonts-arphic-uming
    echo "   ✅ 中文字体安装完成"
else
    echo "   ✅ 中文字体已安装"
fi

# 3. 设置字体环境变量
echo ""
echo "3. 设置字体环境变量..."

# 创建字体配置脚本
cat > set_font_env.sh << 'EOF'
#!/bin/bash
# 字体环境变量设置

# 设置中文locale
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
export LC_CTYPE=zh_CN.UTF-8

# 设置Qt字体
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"
export QT_QPA_PLATFORM_PLUGIN_PATH="/usr/lib/x86_64-linux-gnu/qt5/plugins"

# 设置字体回退
export QT_FONT_DPI=96
export QT_SCALE_FACTOR=1.0

# 强制使用特定字体
export QT_QPA_GENERIC_PLUGINS="fontconfig"

echo "字体环境变量已设置"
echo "LANG=$LANG"
echo "QT_QPA_FONTDIR=$QT_QPA_FONTDIR"
EOF

chmod +x set_font_env.sh
echo "   ✅ 字体环境脚本已创建: set_font_env.sh"

# 4. 创建字体测试程序
echo ""
echo "4. 创建字体测试程序..."

cat > font_test.cpp << 'EOF'
#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QLabel>
#include <QFont>
#include <QFontDatabase>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序字体
    QFont font;
    font.setFamily("WenQuanYi Micro Hei");
    font.setPointSize(12);
    app.setFont(font);
    
    QWidget window;
    window.setWindowTitle("字体测试");
    window.resize(400, 300);
    
    QVBoxLayout *layout = new QVBoxLayout(&window);
    
    // 测试各种中文文本
    QLabel *label1 = new QLabel("测试中文显示: 模型转换器", &window);
    QLabel *label2 = new QLabel("YOLO模型转换工具", &window);
    QLabel *label3 = new QLabel("选择模型文件 (.pt, .onnx)", &window);
    QLabel *label4 = new QLabel("🚀 开始转换", &window);
    
    layout->addWidget(label1);
    layout->addWidget(label2);
    layout->addWidget(label3);
    layout->addWidget(label4);
    
    // 显示可用字体
    QStringList fontFamilies = QFontDatabase().families();
    qDebug() << "可用字体数量:" << fontFamilies.size();
    
    for (const QString &family : fontFamilies) {
        if (family.contains("Hei") || family.contains("YaHei") || family.contains("WenQuanYi")) {
            qDebug() << "中文字体:" << family;
        }
    }
    
    window.show();
    return app.exec();
}
EOF

# 编译字体测试程序
echo "   编译字体测试程序..."
g++ -o font_test font_test.cpp $(pkg-config --cflags --libs Qt5Widgets)

if [ $? -eq 0 ]; then
    echo "   ✅ 字体测试程序编译成功"
else
    echo "   ❌ 字体测试程序编译失败"
fi

# 5. 更新ModelConverter的字体设置
echo ""
echo "5. 更新ModelConverter字体设置..."

# 创建字体修复补丁
cat > font_fix.patch << 'EOF'
--- conversionwindow.cpp.orig
+++ conversionwindow.cpp
@@ setupStyles() {
     setStyleSheet(R"(
         QWidget {
-            font-family: "Microsoft YaHei", "SimHei", sans-serif;
+            font-family: "WenQuanYi Micro Hei", "Microsoft YaHei", "SimHei", "DejaVu Sans", sans-serif;
             font-size: 12px;
+            background-color: #f5f5f5;
         }
         
         QLabel {
             color: #2c3e50;
+            font-weight: normal;
         }
         
         QGroupBox {
             font-weight: bold;
+            font-size: 13px;
             border: 2px solid #bdc3c7;
             border-radius: 5px;
             margin-top: 10px;
             padding-top: 10px;
+            background-color: white;
         }
         
         QComboBox, QLineEdit {
             padding: 6px;
             border: 1px solid #bdc3c7;
             border-radius: 3px;
+            background-color: white;
+            font-size: 12px;
         }
         
         QPushButton {
             padding: 8px 16px;
             border: none;
             border-radius: 4px;
             font-weight: bold;
+            font-size: 12px;
+            background-color: #3498db;
+            color: white;
         }
         
         QTextEdit {
             border: 1px solid #bdc3c7;
             border-radius: 3px;
-            font-family: "Consolas", "Monaco", monospace;
+            font-family: "DejaVu Sans Mono", "Consolas", "Monaco", monospace;
+            font-size: 11px;
+            background-color: white;
         }
     )");
 }
EOF

echo "   ✅ 字体修复补丁已创建"

# 6. 创建完整的启动脚本（包含字体设置）
echo ""
echo "6. 创建完整启动脚本..."

cat > start_with_fonts.sh << 'EOF'
#!/bin/bash
# 带字体修复的完整启动脚本

echo "=== 启动ModelConverter（字体修复版） ==="

# 设置字体环境
source ./set_font_env.sh

# 设置X11环境（WSL2）
if grep -q microsoft /proc/version 2>/dev/null; then
    export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
    echo "WSL2环境，DISPLAY设置为: $DISPLAY"
fi

# 设置库路径
export LD_LIBRARY_PATH="/opt/Qt5.12.6/5.12.6/gcc_64/lib:.:$LD_LIBRARY_PATH"

# 进入build目录
cd build

# 启动程序
echo "正在启动ModelConverter..."
./ModelConverter

echo "程序已退出"
EOF

chmod +x start_with_fonts.sh
echo "   ✅ 完整启动脚本已创建: start_with_fonts.sh"

echo ""
echo "=== 字体修复完成 ==="
echo ""
echo "使用方法："
echo "1. 测试字体显示:"
echo "   source ./set_font_env.sh && ./font_test"
echo ""
echo "2. 启动ModelConverter:"
echo "   ./start_with_fonts.sh"
echo ""
echo "3. 手动设置环境:"
echo "   source ./set_font_env.sh"
echo "   cd build && ./ModelConverter"
echo ""
echo "字体修复要点："
echo "- 安装了WenQuanYi中文字体"
echo "- 设置了正确的locale环境变量"
echo "- 配置了Qt字体路径"
echo "- 更新了应用程序样式表"

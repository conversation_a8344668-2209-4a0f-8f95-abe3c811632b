# 指定要使用的 Qt 安装前缀 (支持多架构)
# 检测架构并设置相应的Qt路径
contains(QT_ARCH, arm64)|contains(QT_ARCH, aarch64) {
    # ARM64架构使用系统Qt
    QMAKE_PREFIX_PATH = /usr/lib/aarch64-linux-gnu/qt5
} else {
    # x86_64架构使用自定义Qt路径
    QMAKE_PREFIX_PATH = /media/rhs/t161/qt512/5.12.6/gcc_64
}

QT       += core gui widgets
TARGET   = ModelConverter
CONFIG   += c++17

# 禁用 sized-deallocation，防止库里对 operator delete(void*,size_t) 的依赖
QMAKE_CXXFLAGS += -fno-sized-deallocation

# 动态库导入声明
DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT

# 链接Qt库 (支持多架构)
contains(QT_ARCH, arm64)|contains(QT_ARCH, aarch64) {
    # ARM64架构使用系统Qt库
    LIBS += -lQt5Widgets \
            -lQt5Gui \
            -lQt5Core
} else {
    # x86_64架构使用自定义Qt库
    LIBS += -L$$QMAKE_PREFIX_PATH/lib \
            -lQt5Widgets \
            -lQt5Gui \
            -lQt5Core
}

# 链接第三方的 sf-core-ls 动态库（.so 放在项目根目录）
LIBS += -L$$PWD -lsf-core-ls

# 设置运行时 rpath (支持多架构)
contains(QT_ARCH, arm64)|contains(QT_ARCH, aarch64) {
    # ARM64架构设置系统库路径
    QMAKE_RPATHDIR += /usr/lib/aarch64-linux-gnu/qt5/lib \
                      $$PWD
} else {
    # x86_64架构设置自定义库路径
    QMAKE_RPATHDIR += $$QMAKE_PREFIX_PATH/lib \
                      $$PWD
}

# 资源、源码、头文件列表
RESOURCES += ModelConverter.qrc

SOURCES  += main.cpp \
            activationwindow.cpp \
            conversionwindow.cpp \
            sized_delete.cpp

HEADERS  += activationwindow.h \
            conversionwindow.h
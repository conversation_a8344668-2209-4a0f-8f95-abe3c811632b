#!/bin/bash
# YOLO模型转换工具 - Linux主机环境运行脚本

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo_info "=== YOLO模型转换工具启动脚本 ==="
echo_info "环境：Linux主机 + Docker + Qt5.12.6"

# 1. 检查必要的环境
echo_info "1. 检查运行环境..."

# 检查是否在Linux环境中
# 检查是否在WSL2中
# if ! grep -q microsoft /proc/version; then
#     echo_error "请在WSL2环境中运行此脚本"
# fi
if ! uname -a | grep -q "Linux"; then
    echo_error "此脚本需要在Linux环境中运行"
    exit 1
fi

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    echo_error "Docker未安装或不可用，请确保Docker Desktop已启动"
    exit 1
fi

# 检查Docker是否正常运行
if ! docker info &> /dev/null; then
    echo_error "Docker服务未运行，请启动Docker Desktop"
    exit 1
fi

echo_success "环境检查通过"

# 2. 加载Docker镜像
echo_info "2. 加载Docker镜像..."
if [ -f "model-converter.tar" ]; then
    echo_info "正在加载镜像文件 model-converter.tar..."
    docker load -i model-converter.tar
    echo_success "Docker镜像加载完成"
else
    echo_error "未找到 model-converter.tar 文件"
    echo_info "请确保 model-converter.tar 文件在当前目录中"
    exit 1
fi

# 3. 验证镜像是否加载成功
echo_info "3. 验证Docker镜像..."
if docker images | grep -q "model-converter"; then
    echo_success "Docker镜像验证成功"
    docker images | grep model-converter
else
    echo_error "Docker镜像加载失败"
    exit 1
fi

# 4. 准备Qt应用程序
echo_info "4. 准备Qt应用程序..."

# 检查Qt应用程序目录
if [ ! -d "autobit/ModelConverter" ]; then
    echo_error "未找到 autobit/ModelConverter 目录"
    exit 1
fi

cd autobit/ModelConverter

# 检查是否已编译
if [ ! -f "build/ModelConverter" ]; then
    echo_warning "未找到已编译的程序，开始编译..."

    # 创建build目录
    mkdir -p build
    cd build

    # 设置Qt环境变量（Ubuntu WSL2环境）
    # Qt5已通过apt安装，应该在系统PATH中

    # 检查Qt是否可用
    if ! command -v qmake &> /dev/null; then
        echo_error "未找到qmake，请安装Qt5开发环境"
        echo_info "运行以下命令安装Qt5："
        echo_info "sudo apt install -y qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
        exit 1
    fi

    echo_info "Qt版本: $(qmake --version | grep Qt)"

    # 编译项目
    echo_info "正在编译Qt项目..."
    qmake ../ModelConverter.pro
    make -j$(nproc)

    if [ $? -eq 0 ]; then
        echo_success "Qt项目编译成功"
    else
        echo_error "Qt项目编译失败"
        exit 1
    fi

    cd ..
else
    echo_success "找到已编译的程序"
fi

# 5. 设置运行环境
echo_info "5. 设置运行环境..."

# Ubuntu WSL2环境下Qt库已在系统路径中，无需额外设置

# 创建必要的目录
mkdir -p build/models
mkdir -p build/result

echo_success "运行环境设置完成"

# 6. 启动应用程序
echo_info "6. 启动YOLO模型转换工具..."
echo_info "激活参数（测试用）："
echo_info "  设备号: 50bf70582c5ea2ac7502656f8cfb522e"
echo_info "  产品号: 40201"
echo_info "  激活码: E76G-JEQR-EQRA-T7ZW"

cd build

# 检查显示环境（WSL2中运行GUI需要X11转发）
if [ -z "$DISPLAY" ]; then
    echo_warning "未设置DISPLAY环境变量"
    echo_info "正在自动设置DISPLAY变量..."
    export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
    echo_info "DISPLAY已设置为: $DISPLAY"
    echo_info "请确保Windows中的VcXsrv X11服务器正在运行"
else
    echo_success "DISPLAY环境变量已设置: $DISPLAY"
fi

# 测试X11连接
echo_info "测试X11连接..."
if command -v xeyes &> /dev/null; then
    timeout 3 xeyes &>/dev/null &
    if [ $? -eq 0 ]; then
        echo_success "X11连接正常"
    else
        echo_warning "X11连接可能有问题，但继续尝试启动GUI"
    fi
fi

echo_info "正在启动ModelConverter..."

# 确保动态库可以被找到
if [ ! -f "libsf-core-ls.so" ]; then
    echo_info "复制动态库文件..."
    cp ../libsf-core-ls.so .
fi

# 设置库路径
export LD_LIBRARY_PATH=.:$LD_LIBRARY_PATH

# 设置字体环境变量，解决X11字体显示问题
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"
export QT_QPA_PLATFORM_PLUGIN_PATH="/usr/lib/x86_64-linux-gnu/qt5/plugins"

# 检查依赖
echo_info "检查程序依赖..."
ldd ./ModelConverter | grep "not found" && echo_warning "发现缺失的依赖库" || echo_success "所有依赖库都可用"

./ModelConverter

echo_success "程序已启动"
#include "activationwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QDebug>
#include <QApplication>
#include <QDesktopWidget>
#include <QScreen>
#include <QCryptographicHash>
#include <QNetworkInterface>
#include <QSysInfo>

#include "SFCoreIntf.h"
#include "YXPermission.h"

ActivationWindow::ActivationWindow(QWidget *parent)
    : QWidget(parent), convWindow(nullptr), isDebugMode(false)
{
    // 初始化许可证管理器
    licenseManager = new LicenseManager(this);
    connect(licenseManager, &LicenseManager::activationFinished,
            this, &ActivationWindow::onActivationFinished);
    connect(licenseManager, &LicenseManager::licenseStatusChanged,
            this, &ActivationWindow::onLicenseStatusChanged);

    setupUI();
    setupStyles();
    setupAnimations();

    // 检查是否已有有效许可证
    if (licenseManager->isLicenseValid()) {
        setStatus("检测到有效许可证，可直接进入主界面", "success");
        activateButton->setText("进入主界面");
    }

    // 居中显示窗口
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);
}

void ActivationWindow::setupUI()
{
    setWindowTitle("模型转换器 - 激活");
    setFixedSize(480, 600);
    setWindowFlags(Qt::Window | Qt::WindowCloseButtonHint);

    // 主框架
    mainFrame = new QFrame(this);
    mainFrame->setObjectName("mainFrame");

    // 标题和Logo
    titleLabel = new QLabel("模型转换器", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);

    logoLabel = new QLabel("🤖", this);
    logoLabel->setObjectName("logoLabel");
    logoLabel->setAlignment(Qt::AlignCenter);

    // 激活码输入
    QLabel *licenseLabel = new QLabel("激活码:", this);
    licenseLabel->setObjectName("inputLabel");

    licenseEdit = new QLineEdit(this);
    licenseEdit->setObjectName("licenseEdit");
    licenseEdit->setPlaceholderText("请输入激活码 (格式: XXXX-XXXX-XXXX-XXXX)");

    // 调试模式
    debugModeCheckBox = new QCheckBox("调试模式 (开发测试)", this);
    debugModeCheckBox->setObjectName("debugCheckBox");

    debugDeviceCombo = new QComboBox(this);
    debugDeviceCombo->setObjectName("debugCombo");
    debugDeviceCombo->addItems({
        "设备1 - 50bf70582c5ea2ac7502656f8cfb522e",
        "设备2 - 60cf80693d6fb3bd8613767f9dfc633f",
        "设备3 - 70df90704e7fc4ce9724878f0efd744g"
    });
    debugDeviceCombo->setVisible(false);

    // 设备信息
    deviceInfoButton = new QPushButton("查看设备信息", this);
    deviceInfoButton->setObjectName("infoButton");

    deviceInfoLabel = new QLabel(this);
    deviceInfoLabel->setObjectName("deviceInfoLabel");
    deviceInfoLabel->setWordWrap(true);
    deviceInfoLabel->setText("设备ID: " + getCurrentDeviceId());

    // 激活按钮
    activateButton = new QPushButton("激活", this);
    activateButton->setObjectName("activateButton");
    activateButton->setDefault(true);

    // 状态标签
    statusLabel = new QLabel("请输入激活码进行激活", this);
    statusLabel->setObjectName("statusLabel");
    statusLabel->setAlignment(Qt::AlignCenter);
    statusLabel->setWordWrap(true);

    // 布局
    QVBoxLayout *mainLayout = new QVBoxLayout(mainFrame);
    mainLayout->setSpacing(20);
    mainLayout->setContentsMargins(40, 40, 40, 40);

    // 标题区域
    QVBoxLayout *headerLayout = new QVBoxLayout();
    headerLayout->addWidget(logoLabel);
    headerLayout->addWidget(titleLabel);
    headerLayout->setSpacing(10);

    // 输入区域
    QVBoxLayout *inputLayout = new QVBoxLayout();
    inputLayout->addWidget(licenseLabel);
    inputLayout->addWidget(licenseEdit);
    inputLayout->addWidget(debugModeCheckBox);
    inputLayout->addWidget(debugDeviceCombo);
    inputLayout->setSpacing(10);

    // 设备信息区域
    QVBoxLayout *deviceLayout = new QVBoxLayout();
    deviceLayout->addWidget(deviceInfoButton);
    deviceLayout->addWidget(deviceInfoLabel);
    deviceLayout->setSpacing(10);

    // 按钮区域
    QVBoxLayout *buttonLayout = new QVBoxLayout();
    buttonLayout->addWidget(activateButton);
    buttonLayout->addWidget(statusLabel);
    buttonLayout->setSpacing(15);

    mainLayout->addLayout(headerLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(inputLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(deviceLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);

    // 主窗口布局
    QVBoxLayout *windowLayout = new QVBoxLayout(this);
    windowLayout->setContentsMargins(0, 0, 0, 0);
    windowLayout->addWidget(mainFrame);

    // 连接信号
    connect(activateButton, &QPushButton::clicked, this, &ActivationWindow::onActivateClicked);
    connect(debugModeCheckBox, &QCheckBox::toggled, this, &ActivationWindow::onDebugModeChanged);
    connect(deviceInfoButton, &QPushButton::clicked, this, &ActivationWindow::showDeviceInfo);
    connect(licenseEdit, &QLineEdit::returnPressed, this, &ActivationWindow::onActivateClicked);
}

void ActivationWindow::setupStyles()
{
    setStyleSheet(R"(
        QWidget {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background-color: #f8f9fa;
        }

        #mainFrame {
            background-color: white;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        #titleLabel {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
        }

        #logoLabel {
            font-size: 48px;
            margin: 10px 0;
        }

        #inputLabel {
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }

        #licenseEdit {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            background-color: #ffffff;
        }

        #licenseEdit:focus {
            border-color: #007bff;
            outline: none;
        }

        #debugCheckBox {
            font-size: 12px;
            color: #6c757d;
        }

        #debugCombo {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 12px;
            background-color: #f8f9fa;
        }

        #infoButton {
            padding: 8px 16px;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
        }

        #infoButton:hover {
            background-color: #5a6268;
        }

        #deviceInfoLabel {
            font-size: 11px;
            color: #6c757d;
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        #activateButton {
            padding: 14px 32px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
        }

        #activateButton:hover {
            background-color: #0056b3;
        }

        #activateButton:pressed {
            background-color: #004085;
        }

        #activateButton:disabled {
            background-color: #6c757d;
        }

        #statusLabel {
            font-size: 13px;
            color: #495057;
            padding: 8px;
        }

        #statusLabel[type="success"] {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
        }

        #statusLabel[type="error"] {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
        }

        #statusLabel[type="warning"] {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
        }
    )");

    // 添加阴影效果
    QGraphicsDropShadowEffect *shadowEffect = new QGraphicsDropShadowEffect(this);
    shadowEffect->setBlurRadius(20);
    shadowEffect->setColor(QColor(0, 0, 0, 30));
    shadowEffect->setOffset(0, 4);
    mainFrame->setGraphicsEffect(shadowEffect);
}

void ActivationWindow::setupAnimations()
{
    statusAnimation = new QPropertyAnimation(statusLabel, "geometry", this);
    statusAnimation->setDuration(300);
    statusAnimation->setEasingCurve(QEasingCurve::OutCubic);

    statusTimer = new QTimer(this);
    statusTimer->setSingleShot(true);
}

void ActivationWindow::setStatus(const QString &message, const QString &type)
{
    statusLabel->setText(message);
    statusLabel->setProperty("type", type);
    statusLabel->style()->unpolish(statusLabel);
    statusLabel->style()->polish(statusLabel);

    // 简单的淡入效果
    statusLabel->setVisible(false);
    statusLabel->setVisible(true);
}

void ActivationWindow::onDebugModeChanged(bool enabled)
{
    isDebugMode = enabled;
    debugDeviceCombo->setVisible(enabled);

    // 设置许可证管理器的调试模式
    licenseManager->setDebugMode(enabled);

    if (enabled) {
        setStatus("调试模式已启用 - 将使用模拟激活", "warning");
        // 填充调试设备列表
        debugDeviceCombo->clear();
        debugDeviceCombo->addItems(licenseManager->getDebugDevices());
    } else {
        setStatus("请输入激活码进行激活", "info");
    }
}

void ActivationWindow::showDeviceInfo()
{
    QString deviceId = getCurrentDeviceId();
    QString systemInfo = QString("系统: %1\n架构: %2\n设备ID: %3")
                        .arg(QSysInfo::prettyProductName())
                        .arg(QSysInfo::currentCpuArchitecture())
                        .arg(deviceId);

    QMessageBox::information(this, "设备信息", systemInfo);
}

QString ActivationWindow::getCurrentDeviceId()
{
    // 获取网络接口MAC地址作为设备标识
    QString deviceId;
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();

    for (const QNetworkInterface &interface : interfaces) {
        if (interface.flags().testFlag(QNetworkInterface::IsUp) &&
            interface.flags().testFlag(QNetworkInterface::IsRunning) &&
            !interface.flags().testFlag(QNetworkInterface::IsLoopBack)) {

            QString mac = interface.hardwareAddress();
            if (!mac.isEmpty()) {
                // 使用MAC地址生成设备ID
                QCryptographicHash hash(QCryptographicHash::Md5);
                hash.addData(mac.toUtf8());
                hash.addData("AUTOBIT_DEVICE_SALT");
                deviceId = hash.result().toHex();
                break;
            }
        }
    }

    // 如果没有找到网络接口，使用系统信息生成
    if (deviceId.isEmpty()) {
        QCryptographicHash hash(QCryptographicHash::Md5);
        hash.addData(QSysInfo::machineHostName().toUtf8());
        hash.addData(QSysInfo::currentCpuArchitecture().toUtf8());
        hash.addData("AUTOBIT_FALLBACK_SALT");
        deviceId = hash.result().toHex();
    }

    return deviceId;
}

void ActivationWindow::onActivateClicked()
{
    // 如果已有有效许可证，直接进入主界面
    if (licenseManager->isLicenseValid()) {
        showActivationSuccess();
        return;
    }

    QString license = licenseEdit->text().trimmed();
    if (license.isEmpty()) {
        setStatus("请输入激活码！", "error");
        return;
    }

    // 禁用按钮，显示加载状态
    activateButton->setEnabled(false);
    activateButton->setText("激活中...");
    setStatus("正在验证激活码...", "info");

    // 使用许可证管理器进行激活
    licenseManager->activateLicense(license);
}

void ActivationWindow::onActivationFinished(LicenseManager::ActivationResult result, const QString &message)
{
    // 恢复按钮状态
    activateButton->setEnabled(true);

    if (result == LicenseManager::Success) {
        activateButton->setText("进入主界面");
        showActivationSuccess();
    } else {
        activateButton->setText("激活");

        QString errorMsg = message;
        switch (result) {
            case LicenseManager::InvalidLicense:
                errorMsg = "无效的激活码：" + message;
                break;
            case LicenseManager::NetworkError:
                errorMsg = "网络连接失败：" + message;
                break;
            case LicenseManager::ServerError:
                errorMsg = "服务器错误：" + message;
                break;
            case LicenseManager::HardwareMismatch:
                errorMsg = "硬件不匹配：此激活码已绑定到其他设备";
                break;
            case LicenseManager::LicenseExpired:
                errorMsg = "许可证已过期：" + message;
                break;
            case LicenseManager::LicenseExhausted:
                errorMsg = "许可证激活次数已用尽：" + message;
                break;
            default:
                errorMsg = "激活失败：" + message;
                break;
        }

        showActivationError(errorMsg);
    }
}

void ActivationWindow::onLicenseStatusChanged(bool isValid)
{
    if (isValid) {
        setStatus("许可证状态：有效", "success");
    } else {
        setStatus("许可证状态：无效", "error");
    }
}

void ActivationWindow::showActivationSuccess()
{
    setStatus("✅ 激活成功！正在启动模型转换器...", "success");

    // 延迟启动转换窗口，让用户看到成功消息
    QTimer::singleShot(1500, [this]() {
        if (!convWindow) {
            convWindow = new ConversionWindow();
        }
        convWindow->show();
        this->close();
    });
}

void ActivationWindow::showActivationError(const QString &error)
{
    setStatus("❌ " + error, "error");

    // 清空输入框，让用户重新输入
    licenseEdit->clear();
    licenseEdit->setFocus();
}

#!/usr/bin/env python3
# 创建简单的状态图标
import os
from PIL import Image, ImageDraw

def create_icon(color, filename, size=64):
    """创建简单的圆形状态图标"""
    img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形
    margin = 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill=color, outline='white', width=2)
    
    # 根据状态添加符号
    if 'ready' in filename:
        # 绘制问号
        draw.text((size//2-8, size//2-12), '?', fill='white', anchor='mm')
    elif 'working' in filename:
        # 绘制旋转符号
        draw.text((size//2-8, size//2-12), '⟳', fill='white', anchor='mm')
    elif 'success' in filename:
        # 绘制对勾
        draw.text((size//2-8, size//2-12), '✓', fill='white', anchor='mm')
    elif 'error' in filename:
        # 绘制叉号
        draw.text((size//2-8, size//2-12), '✗', fill='white', anchor='mm')
    
    img.save(filename)

# 创建图标目录
icons_dir = 'icons'
os.makedirs(icons_dir, exist_ok=True)

# 创建各种状态图标
create_icon('#6c757d', os.path.join(icons_dir, 'ready.png'))      # 灰色 - 准备
create_icon('#007bff', os.path.join(icons_dir, 'working.png'))    # 蓝色 - 工作中
create_icon('#28a745', os.path.join(icons_dir, 'success.png'))    # 绿色 - 成功
create_icon('#dc3545', os.path.join(icons_dir, 'error.png'))      # 红色 - 错误

print("图标创建完成!")

I rknn-toolkit2 version: 2.3.2
--> Config model
done
--> Loading model

I Loading :   0%|                                                           | 0/123 [00:00<?, ?it/s]
I Loading : 100%|██████████████████████████████████████████████| 123/123 [00:00<00:00, 19587.64it/s]
done
--> Building model

I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   1%|▍                                               | 1/100 [00:00<00:00, 169.06it/s]
I OpFusing 0:   2%|▉                                               | 2/100 [00:00<00:00, 314.84it/s]
I OpFusing 0:   3%|█▍                                              | 3/100 [00:00<00:00, 444.16it/s]
I OpFusing 0:   4%|█▉                                              | 4/100 [00:00<00:00, 556.77it/s]
I OpFusing 0:   6%|██▉                                             | 6/100 [00:00<00:00, 522.24it/s]
I OpFusing 0:   8%|███▊                                            | 8/100 [00:00<00:00, 599.70it/s]
I OpFusing 0:   9%|████▎                                           | 9/100 [00:00<00:00, 654.10it/s]
I OpFusing 0:  10%|████▋                                          | 10/100 [00:00<00:00, 704.33it/s]
I OpFusing 0:  11%|█████▏                                         | 11/100 [00:00<00:00, 753.26it/s]
I OpFusing 0:  12%|█████▋                                         | 12/100 [00:00<00:00, 799.05it/s]
I OpFusing 0:  13%|██████                                         | 13/100 [00:00<00:00, 834.65it/s]
I OpFusing 0:  15%|███████                                        | 15/100 [00:00<00:00, 934.89it/s]
I OpFusing 0:  16%|███████▌                                       | 16/100 [00:00<00:00, 962.87it/s]
I OpFusing 0:  18%|████████▎                                     | 18/100 [00:00<00:00, 1042.42it/s]
I OpFusing 0:  19%|████████▋                                     | 19/100 [00:00<00:00, 1075.35it/s]
I OpFusing 0:  20%|█████████▏                                    | 20/100 [00:00<00:00, 1105.98it/s]
I OpFusing 0:  21%|█████████▋                                    | 21/100 [00:00<00:00, 1136.21it/s]
I OpFusing 0:  22%|██████████                                    | 22/100 [00:00<00:00, 1165.82it/s]
I OpFusing 0:  24%|███████████                                   | 24/100 [00:00<00:00, 1233.88it/s]
I OpFusing 0:  25%|███████████▌                                  | 25/100 [00:00<00:00, 1260.00it/s]
I OpFusing 0:  27%|████████████▍                                 | 27/100 [00:00<00:00, 1317.02it/s]
I OpFusing 0:  28%|████████████▉                                 | 28/100 [00:00<00:00, 1339.83it/s]
I OpFusing 0:  30%|█████████████▊                                | 30/100 [00:00<00:00, 1396.86it/s]
I OpFusing 0:  32%|██████████████▋                               | 32/100 [00:00<00:00, 1439.47it/s]
I OpFusing 0:  33%|███████████████▏                              | 33/100 [00:00<00:00, 1455.18it/s]
I OpFusing 0:  35%|████████████████                              | 35/100 [00:00<00:00, 1514.14it/s]
I OpFusing 0:  36%|████████████████▌                             | 36/100 [00:00<00:00, 1530.58it/s]
I OpFusing 0:  37%|█████████████████                             | 37/100 [00:00<00:00, 1546.11it/s]
I OpFusing 0:  39%|█████████████████▉                            | 39/100 [00:00<00:00, 1589.99it/s]
I OpFusing 0:  41%|██████████████████▊                           | 41/100 [00:00<00:00, 1615.33it/s]
I OpFusing 0:  43%|███████████████████▊                          | 43/100 [00:00<00:00, 1666.31it/s]
I OpFusing 0:  48%|██████████████████████                        | 48/100 [00:00<00:00, 1668.21it/s]
I OpFusing 0:  50%|███████████████████████                       | 50/100 [00:00<00:00, 1711.96it/s]
I OpFusing 0:  52%|███████████████████████▉                      | 52/100 [00:00<00:00, 1733.76it/s]
I OpFusing 0:  54%|████████████████████████▊                     | 54/100 [00:00<00:00, 1775.65it/s]
I OpFusing 0:  55%|█████████████████████████▎                    | 55/100 [00:00<00:00, 1780.53it/s]
I OpFusing 0:  57%|██████████████████████████▏                   | 57/100 [00:00<00:00, 1817.77it/s]
I OpFusing 0:  59%|███████████████████████████▏                  | 59/100 [00:00<00:00, 1843.69it/s]
I OpFusing 0:  60%|███████████████████████████▌                  | 60/100 [00:00<00:00, 1849.31it/s]
I OpFusing 0:  63%|████████████████████████████▉                 | 63/100 [00:00<00:00, 1900.20it/s]
I OpFusing 0:  65%|█████████████████████████████▉                | 65/100 [00:00<00:00, 1935.44it/s]
I OpFusing 0:  66%|██████████████████████████████▎               | 66/100 [00:00<00:00, 1941.43it/s]
I OpFusing 0:  68%|███████████████████████████████▎              | 68/100 [00:00<00:00, 1973.90it/s]
I OpFusing 0:  70%|████████████████████████████████▏             | 70/100 [00:00<00:00, 1997.26it/s]
I OpFusing 0:  72%|█████████████████████████████████             | 72/100 [00:00<00:00, 2030.51it/s]
I OpFusing 0:  74%|██████████████████████████████████            | 74/100 [00:00<00:00, 2050.83it/s]
I OpFusing 0:  76%|██████████████████████████████████▉           | 76/100 [00:00<00:00, 2079.80it/s]
I OpFusing 0:  78%|███████████████████████████████████▉          | 78/100 [00:00<00:00, 2109.72it/s]
I OpFusing 0:  80%|████████████████████████████████████▊         | 80/100 [00:00<00:00, 2136.48it/s]
I OpFusing 0:  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 2152.76it/s]
I OpFusing 0:  84%|██████████████████████████████████████▋       | 84/100 [00:00<00:00, 2180.77it/s]
I OpFusing 0:  86%|███████████████████████████████████████▌      | 86/100 [00:00<00:00, 2193.34it/s]
I OpFusing 0:  88%|████████████████████████████████████████▍     | 88/100 [00:00<00:00, 2218.94it/s]
I OpFusing 0:  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 2246.23it/s]
I OpFusing 0:  92%|██████████████████████████████████████████▎   | 92/100 [00:00<00:00, 2261.33it/s]
I OpFusing 0:  95%|███████████████████████████████████████████▋  | 95/100 [00:00<00:00, 2279.81it/s]
I OpFusing 0: 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 1260.40it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 916.47it/s]
I OpFusing 2 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 868.59it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 174.66it/s]
[1;33mW[0m [1;33mbuild: found outlier value, this may affect quantization accuracy
                        const nameabs_mean    abs_std     outlier value
                        344      0.83        1.39        14.282      [0m



I GraphPreparing :   0%|                                                    | 0/149 [00:00<?, ?it/s]
I GraphPreparing : 100%|████████████████████████████████████████| 149/149 [00:00<00:00, 9271.72it/s]

I Quantizating :   0%|                                                      | 0/149 [00:00<?, ?it/s]
I Quantizating :   1%|▎                                             | 1/149 [00:00<00:32,  4.56it/s]
I Quantizating :   1%|▌                                             | 2/149 [00:00<00:40,  3.59it/s]
I Quantizating :   2%|▉                                             | 3/149 [00:01<01:01,  2.37it/s]
I Quantizating :   3%|█▏                                            | 4/149 [00:01<00:54,  2.66it/s]
I Quantizating :   3%|█▌                                            | 5/149 [00:01<00:50,  2.87it/s]
I Quantizating :   5%|██▏                                           | 7/149 [00:01<00:31,  4.54it/s]
I Quantizating :   6%|██▊                                           | 9/149 [00:02<00:22,  6.20it/s]
I Quantizating :   7%|███                                          | 10/149 [00:02<00:22,  6.11it/s]
I Quantizating :   7%|███▎                                         | 11/149 [00:02<00:21,  6.41it/s]
I Quantizating :   9%|████▏                                        | 14/149 [00:02<00:15,  8.77it/s]
I Quantizating :  11%|████▊                                        | 16/149 [00:02<00:12, 10.31it/s]
I Quantizating :  12%|█████▍                                       | 18/149 [00:03<00:19,  6.68it/s]
I Quantizating :  13%|█████▋                                       | 19/149 [00:03<00:18,  6.98it/s]
I Quantizating :  15%|██████▋                                      | 22/149 [00:03<00:12, 10.47it/s]
I Quantizating :  16%|███████▏                                     | 24/149 [00:03<00:11, 10.87it/s]
I Quantizating :  19%|████████▍                                    | 28/149 [00:03<00:08, 15.03it/s]
I Quantizating :  20%|█████████                                    | 30/149 [00:03<00:08, 13.96it/s]
I Quantizating :  22%|█████████▉                                   | 33/149 [00:04<00:06, 16.99it/s]
I Quantizating :  24%|██████████▊                                  | 36/149 [00:04<00:06, 16.55it/s]
I Quantizating :  26%|███████████▍                                 | 38/149 [00:04<00:09, 12.18it/s]
I Quantizating :  28%|████████████▋                                | 42/149 [00:04<00:06, 16.81it/s]
I Quantizating :  30%|█████████████▌                               | 45/149 [00:04<00:05, 17.66it/s]
I Quantizating :  32%|██████████████▍                              | 48/149 [00:04<00:05, 18.03it/s]
I Quantizating :  36%|████████████████                             | 53/149 [00:05<00:04, 20.92it/s]
I Quantizating :  40%|█████████████████▊                           | 59/149 [00:05<00:03, 26.42it/s]
I Quantizating :  42%|██████████████████▋                          | 62/149 [00:05<00:04, 19.42it/s]
I Quantizating :  45%|████████████████████▏                        | 67/149 [00:05<00:03, 22.23it/s]
I Quantizating :  49%|██████████████████████                       | 73/149 [00:05<00:02, 28.89it/s]
I Quantizating :  56%|█████████████████████████                    | 83/149 [00:05<00:01, 43.24it/s]
I Quantizating :  60%|██████████████████████████▉                  | 89/149 [00:06<00:01, 40.02it/s]
I Quantizating :  63%|████████████████████████████▍                | 94/149 [00:06<00:01, 33.29it/s]
I Quantizating :  66%|█████████████████████████████▌               | 98/149 [00:06<00:01, 33.56it/s]
I Quantizating :  68%|██████████████████████████████              | 102/149 [00:06<00:01, 29.93it/s]
I Quantizating :  71%|███████████████████████████████▎            | 106/149 [00:06<00:01, 27.29it/s]
I Quantizating :  74%|████████████████████████████████▍           | 110/149 [00:07<00:01, 22.50it/s]
I Quantizating :  76%|█████████████████████████████████▎          | 113/149 [00:07<00:01, 22.62it/s]
I Quantizating :  78%|██████████████████████████████████▎         | 116/149 [00:07<00:01, 17.67it/s]
I Quantizating :  81%|███████████████████████████████████▍        | 120/149 [00:07<00:01, 21.24it/s]
I Quantizating :  83%|████████████████████████████████████▎       | 123/149 [00:07<00:01, 21.03it/s]
I Quantizating :  86%|█████████████████████████████████████▊      | 128/149 [00:07<00:00, 25.06it/s]
I Quantizating :  88%|██████████████████████████████████████▋     | 131/149 [00:08<00:00, 22.69it/s]
I Quantizating :  92%|████████████████████████████████████████▍   | 137/149 [00:08<00:00, 26.92it/s]
I Quantizating :  96%|██████████████████████████████████████████▏ | 143/149 [00:08<00:00, 31.92it/s]
I Quantizating :  99%|███████████████████████████████████████████▋| 148/149 [00:08<00:00, 29.65it/s]
I Quantizating : 100%|████████████████████████████████████████████| 149/149 [00:08<00:00, 17.23it/s]
[1;33mW[0m [1;33mbuild: The default input dtype of 'images' is changed from 'float32' to 'int8' in rknn model for performance!
                       Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of 'output0' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '340' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '342' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
I rknn building ...
I rknn building done.
done
--> Export rknn model
done

#!/bin/bash
# 修复界面问题脚本 - 恢复原始简单设计

echo "=== 修复ModelConverter界面问题 ==="

# 进入项目目录
cd "$(dirname "$0")"

echo "1. 备份当前复杂版本..."
if [ -f "conversionwindow.cpp" ]; then
    cp conversionwindow.cpp conversionwindow_complex_backup.cpp
    echo "   ✅ 已备份复杂版本到 conversionwindow_complex_backup.cpp"
fi

echo ""
echo "2. 修复转换逻辑..."

# 创建简化的转换逻辑
cat > conversionwindow_fixed.cpp << 'EOF'
#include "conversionwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QFileDialog>
#include <QMessageBox>
#include <QDir>
#include <QFileInfo>
#include <QTimer>
#include <QDebug>
#include <QProgressBar>
#include <QTextEdit>
#include <QProcess>

ConversionWindow::ConversionWindow(QWidget *parent)
    : QWidget(parent), process(nullptr)
{
    setupUI();
    setupConnections();
    setupStyles();
}

void ConversionWindow::setupUI()
{
    setWindowTitle("模型转换器 - 主界面");
    setMinimumSize(800, 600);

    // 主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(20);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 标题
    QLabel *titleLabel = new QLabel("YOLO模型转换工具", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);

    // 输入区域 - 简化设计
    QGroupBox *inputGroup = new QGroupBox("模型配置", this);
    
    // 模型类型选择（只有yolov5和yolov8）
    modelTypeCombo = new QComboBox(this);
    modelTypeCombo->addItems({"yolov5", "yolov8"});

    // 模型文件选择
    modelPathEdit = new QLineEdit(this);
    modelPathEdit->setPlaceholderText("选择模型文件 (.pt, .onnx)");

    browseButton = new QPushButton("浏览", this);
    convertButton = new QPushButton("转换", this);

    // 简单的网格布局
    QGridLayout *inputLayout = new QGridLayout(inputGroup);
    inputLayout->addWidget(new QLabel("模型类型:"), 0, 0);
    inputLayout->addWidget(modelTypeCombo, 0, 1);
    inputLayout->addWidget(new QLabel("模型文件:"), 1, 0);
    inputLayout->addWidget(modelPathEdit, 1, 1);
    inputLayout->addWidget(browseButton, 1, 2);
    inputLayout->addWidget(convertButton, 2, 1);

    mainLayout->addWidget(inputGroup);

    // 输出区域
    resultDisplay = new QTextEdit(this);
    resultDisplay->setReadOnly(true);
    resultDisplay->setMinimumHeight(300);
    
    statusLabel = new QLabel("等待转换", this);
    statusLabel->setAlignment(Qt::AlignCenter);

    mainLayout->addWidget(new QLabel("转换输出:"));
    mainLayout->addWidget(resultDisplay);
    mainLayout->addWidget(statusLabel);
}

void ConversionWindow::setupConnections()
{
    connect(browseButton, &QPushButton::clicked, this, &ConversionWindow::onBrowseClicked);
    connect(convertButton, &QPushButton::clicked, this, &ConversionWindow::onConvertClicked);
}

void ConversionWindow::setupStyles()
{
    // 设置中文字体支持的样式
    setStyleSheet(R"(
        QWidget {
            font-family: "Microsoft YaHei", "SimHei", "DejaVu Sans", sans-serif;
            font-size: 12px;
        }
        
        QLabel {
            color: #2c3e50;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #3498db;
            color: white;
        }
        
        QComboBox, QLineEdit {
            padding: 6px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }
        
        QTextEdit {
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            font-family: "Consolas", "Monaco", monospace;
        }
    )");
}

void ConversionWindow::onBrowseClicked()
{
    QString filePath = QFileDialog::getOpenFileName(
        this, 
        QString::fromUtf8("选择模型文件"), 
        "", 
        "Model Files (*.pt *.onnx)"
    );
    
    if (!filePath.isEmpty()) {
        modelPathEdit->setText(filePath);
        
        // 自动检测模型类型
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName().toLower();
        
        if (fileName.contains("yolov5")) {
            modelTypeCombo->setCurrentText("yolov5");
        } else if (fileName.contains("yolov8")) {
            modelTypeCombo->setCurrentText("yolov8");
        }
        
        resultDisplay->append("✅ 已选择模型文件: " + filePath);
    }
}

void ConversionWindow::onConvertClicked()
{
    convertButton->setEnabled(false);
    convertButton->setText("转换中...");

    QString modelPath = modelPathEdit->text().trimmed();
    if (modelPath.isEmpty()) {
        resultDisplay->append("❌ 请先选择模型文件");
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        return;
    }

    QString modelType = modelTypeCombo->currentText().trimmed();
    QString fileName = QFileInfo(modelPath).fileName();

    // 使用原始的简单转换逻辑（参考autobit项目）
    QString linuxWorkDir = QDir::homePath() + "/yolo_conversion";
    QString linuxModelsDir = linuxWorkDir + "/models";
    QString linuxResultDir = linuxWorkDir + "/result";
    QString windowsResultDir = "/mnt/e/code/autobit_back/yolo-rk3588/result";

    // 创建目录
    QDir().mkpath(linuxModelsDir);
    QDir().mkpath(linuxResultDir);
    QDir().mkpath(windowsResultDir);

    // 复制模型文件
    QString linuxModelPath = linuxModelsDir + "/" + fileName;
    if (QFile::exists(linuxModelPath)) {
        QFile::remove(linuxModelPath);
        resultDisplay->append("🔄 覆盖已存在的模型文件: " + fileName);
    }

    if (!QFile::copy(modelPath, linuxModelPath)) {
        resultDisplay->append("❌ 无法复制模型文件");
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        return;
    }

    QString dockerModelPath = "/workspace/yolo-rk3588/models";
    QString dockerOutputPath = "result";

    // 构造简单的Docker命令（不需要格式和精度参数）
    QString dockerCommand = QString(
        "docker run --rm "
        "-v \"%1\":%2 "
        "-v \"%3\":/workspace/yolo-rk3588/result "
        "-w /workspace/yolo-rk3588 "
        "model-converter:latest "
        "--model %4 --weight %5 --output %6")
        .arg(linuxModelsDir)
        .arg(dockerModelPath)
        .arg(linuxResultDir)
        .arg(modelType)
        .arg(dockerModelPath + "/" + fileName)
        .arg(dockerOutputPath);

    resultDisplay->append("✅ 模型文件已复制");
    resultDisplay->append("🚀 正在执行转换...");
    resultDisplay->append("命令: " + dockerCommand);

    // 启动转换进程
    if (process) {
        process->deleteLater();
    }

    process = new QProcess(this);
    connect(process, &QProcess::readyReadStandardOutput, this, &ConversionWindow::onProcessOutput);
    connect(process, &QProcess::readyReadStandardError, this, &ConversionWindow::onProcessErrorOutput);
    connect(process, QOverload<int>::of(&QProcess::finished), this, &ConversionWindow::onProcessFinished);

    statusLabel->setText("正在转换模型...");
    process->start("bash", QStringList() << "-c" << dockerCommand);
}

void ConversionWindow::onProcessOutput()
{
    if (!process) return;
    
    QByteArray data = process->readAllStandardOutput();
    QString output = QString::fromUtf8(data);
    resultDisplay->append(output);
}

void ConversionWindow::onProcessErrorOutput()
{
    if (!process) return;
    
    QByteArray data = process->readAllStandardError();
    QString output = QString::fromUtf8(data);
    resultDisplay->append("错误: " + output);
}

void ConversionWindow::onProcessFinished(int exitCode)
{
    convertButton->setEnabled(true);
    convertButton->setText("转换");
    
    if (exitCode == 0) {
        resultDisplay->append("🎉 转换完成！");
        statusLabel->setText("转换成功");
        
        // 复制结果到Windows目录
        QString linuxResultDir = QDir::homePath() + "/yolo_conversion/result";
        QString windowsResultDir = "/mnt/e/code/autobit_back/yolo-rk3588/result";
        
        QProcess::execute("bash", QStringList() << "-c" << 
            QString("cp -r %1/* %2/").arg(linuxResultDir).arg(windowsResultDir));
        
        resultDisplay->append("📁 结果已保存到: " + windowsResultDir);
    } else {
        resultDisplay->append("❌ 转换失败，退出码: " + QString::number(exitCode));
        statusLabel->setText("转换失败");
    }
}
EOF

echo "   ✅ 已创建简化版本 conversionwindow_fixed.cpp"

echo ""
echo "3. 替换复杂版本..."
if [ -f "conversionwindow_fixed.cpp" ]; then
    mv conversionwindow.cpp conversionwindow_complex.cpp
    mv conversionwindow_fixed.cpp conversionwindow.cpp
    echo "   ✅ 已替换为简化版本"
else
    echo "   ❌ 简化版本创建失败"
    exit 1
fi

echo ""
echo "4. 重新编译..."
rm -rf build
mkdir -p build
cd build

# 复制许可证库
if [ -f "../libsf-core-ls.so" ]; then
    cp ../libsf-core-ls.so .
fi

# 编译
qmake ../ModelConverter.pro
make -j$(nproc)

if [ $? -eq 0 ]; then
    echo "   🎉 重新编译成功！"
    echo ""
    echo "=== 修复完成 ==="
    echo "界面已恢复为原始的简单设计："
    echo "- 只有模型类型选择（yolov5/yolov8）"
    echo "- 只有一个转换按钮"
    echo "- 移除了复杂的格式和精度选择"
    echo "- 修复了字体显示问题"
    echo ""
    echo "现在可以运行程序："
    echo "cd build && ./ModelConverter"
else
    echo "   ❌ 编译失败"
    exit 1
fi

#!/bin/bash
# build-linux.sh: 修复编译器路径定位至 bin 目录下的真实 gcc

set -e

# 脚本所在目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)

# 检测主机架构并设置编译工具链
HOST_ARCH=$(uname -m)
if [ "$HOST_ARCH" = "aarch64" ]; then
    # 在arm64主机上使用native编译器
    TOOLCHAIN_DIR=""
    USE_NATIVE_COMPILER=true
else
    # 在x86_64主机上使用交叉编译工具链
    TOOLCHAIN_DIR="tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu"
    USE_NATIVE_COMPILER=false
fi

# 解析参数
while getopts ":t:a:d:b:m:r:j:i:o:" opt; do
  case $opt in
    t) TARGET_SOC=$OPTARG ;;  # SoC 型号
    a) TARGET_ARCH=$OPTARG ;; # 架构
    b) BUILD_TYPE=$OPTARG ;;  # Release/Debug
    m) ENABLE_ASAN=ON; export ENABLE_ASAN=TRUE ;;  # ASan
    d) BUILD_DEMO_NAME=$OPTARG ;;  # Demo 名称
    r) DISABLE_RGA=ON ;;  # 禁用 RGA
    j) DISABLE_LIBJPEG=ON ;;  # 禁用 libjpeg
    i) INPUT_PATH=$OPTARG ;;  # 输入目录
    o) OUTPUT_PATH=$OPTARG ;;  # 输出目录
    :) echo "Option -$OPTARG requires an argument." && exit 1;;
    ?) echo "Invalid option: -$OPTARG" && exit 1;;
  esac
done

# 必填检查
if [ -z "${TARGET_SOC:-}" ] || [ -z "${BUILD_DEMO_NAME:-}" ]; then
  echo "Usage: $0 -t <target> -a <arch> -d <demo> -i <input> -o <output> [-b <Release/Debug>] [-m] [-r] [-j]"
  exit 1
fi

# 默认值
BUILD_TYPE=${BUILD_TYPE:-Release}
ENABLE_ASAN=${ENABLE_ASAN:-OFF}
DISABLE_RGA=${DISABLE_RGA:-OFF}
DISABLE_LIBJPEG=${DISABLE_LIBJPEG:-OFF}

# 处理 SoC 别名
case ${TARGET_SOC} in
  rv1103) TARGET_SOC=rv1106;;
  rk3566|rk3568|rk3562) TARGET_SOC=rk356x;;
esac

# 设置编译器
if [ "$USE_NATIVE_COMPILER" = true ]; then
    # 使用native编译器
    export CC="gcc"
    export CXX="g++"
    echo "Using native compilers for arm64 host"
else
    # 使用交叉编译器
    CC_BIN_DIR="${SCRIPT_DIR}/${TOOLCHAIN_DIR}/bin"
    # 如果 bin 下没有 aarch64-linux-gnu-gcc，则尝试子目录 aarch64-linux-gnu/bin
    if [ ! -f "${CC_BIN_DIR}/aarch64-linux-gnu-gcc" ]; then
      CC_BIN_DIR="${SCRIPT_DIR}/${TOOLCHAIN_DIR}/aarch64-linux-gnu/bin"
    fi

    # 定义编译器
    export CC="${CC_BIN_DIR}/aarch64-linux-gnu-gcc"
    export CXX="${CC_BIN_DIR}/aarch64-linux-gnu-g++"

    # 检查编译器可用性
    if [ ! -x "${CC}" ]; then
      echo "Compiler not found or not executable: ${CC}" && exit 1
    fi
fi

# 验证编译器可用性
if ! command -v "${CC}" >/dev/null 2>&1; then
    echo "Error: Compiler ${CC} not found or not executable" && exit 1
fi

# Demo 路径
BUILD_DEMO_PATH="${SCRIPT_DIR}/${BUILD_DEMO_NAME}/cpp"
if [ ! -d "${BUILD_DEMO_PATH}" ]; then
  echo "Cannot find demo directory: ${BUILD_DEMO_PATH}" && exit 1
fi

# 输出目录设定
TARGET_SDK="rknn_${BUILD_DEMO_NAME}_demo"
TARGET_PLATFORM="${TARGET_SOC}_linux_${TARGET_ARCH}"
INSTALL_DIR="${OUTPUT_PATH}/install/${TARGET_PLATFORM}/${TARGET_SDK}"
BUILD_DIR="${OUTPUT_PATH}/build/build_${TARGET_SDK}_${TARGET_PLATFORM}_${BUILD_TYPE}"

# 打印配置
cat <<-EOL
===================================
BUILD_DEMO_NAME:   ${BUILD_DEMO_NAME}
BUILD_DEMO_PATH:   ${BUILD_DEMO_PATH}
TARGET_SOC:        ${TARGET_SOC}
TARGET_ARCH:       ${TARGET_ARCH}
BUILD_TYPE:        ${BUILD_TYPE}
ENABLE_ASAN:       ${ENABLE_ASAN}
DISABLE_RGA:       ${DISABLE_RGA}
DISABLE_LIBJPEG:   ${DISABLE_LIBJPEG}
CC:                ${CC}
CXX:               ${CXX}
INPUT_PATH:        ${INPUT_PATH}
OUTPUT_PATH:       ${OUTPUT_PATH}
INSTALL_DIR:       ${INSTALL_DIR}
BUILD_DIR:         ${BUILD_DIR}
===================================
EOL

# 创建并清理目录
mkdir -p "${BUILD_DIR}"
rm -rf "${INSTALL_DIR}"

# 编译流程
cd "${BUILD_DIR}"
cmake "${BUILD_DEMO_PATH}" \
    -DTARGET_SOC=${TARGET_SOC} \
    -DCMAKE_SYSTEM_NAME=Linux \
    -DCMAKE_SYSTEM_PROCESSOR=${TARGET_ARCH} \
    -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
    -DENABLE_ASAN=${ENABLE_ASAN} \
    -DDISABLE_RGA=${DISABLE_RGA} \
    -DDISABLE_LIBJPEG=${DISABLE_LIBJPEG} \
    -DCMAKE_INSTALL_PREFIX="${INSTALL_DIR}" \
    -DINPUT_DIR="${INPUT_PATH}" \
    -DOpenCV_DIR=/usr/include/opencv4
make -j$(nproc)
make install

# 检查 RKNN 模型
suffix=".rknn"
shopt -s nullglob
if ! compgen -G "${INSTALL_DIR}/model/*${suffix}" >/dev/null; then
  echo -e "\e[91mThe RKNN model cannot be found in \"${INSTALL_DIR}/model\"\e[0m"
fi

#!/bin/bash
# autobit_back ARM64 Debian 启动脚本
# 适用于 ARM64 架构的 Debian 系统

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo_info "=== autobit_back ARM64 Debian 启动脚本 ==="

# 检测架构
ARCH=$(uname -m)
if [ "$ARCH" != "aarch64" ]; then
    echo_warning "检测到非ARM64架构: $ARCH"
    echo_warning "此脚本专为ARM64架构的Debian系统设计"
    echo_warning "如果您在x86_64系统上运行，请使用 run_converter.sh"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检测操作系统
if [ -f /etc/debian_version ]; then
    echo_info "检测到Debian系统"
elif [ -f /etc/os-release ]; then
    . /etc/os-release
    echo_info "检测到操作系统: $NAME"
else
    echo_warning "无法确定操作系统类型"
fi

# 检查依赖
echo_info "检查系统依赖..."

# 检查Docker
if ! command -v docker >/dev/null 2>&1; then
    echo_error "Docker未安装，请先安装Docker"
    echo_info "安装命令: sudo apt update && sudo apt install -y docker.io"
    exit 1
fi

# 检查Qt5开发库
if ! dpkg -l | grep -q qtbase5-dev; then
    echo_warning "Qt5开发库未安装，正在安装..."
    sudo apt update
    sudo apt install -y qtbase5-dev qt5-qmake qtbase5-dev-tools libqt5widgets5 libqt5gui5 libqt5core5a
    echo_success "Qt5开发库安装完成"
else
    echo_success "Qt5开发库已安装"
fi

# 检查构建工具
if ! command -v gcc >/dev/null 2>&1 || ! command -v g++ >/dev/null 2>&1 || ! command -v make >/dev/null 2>&1; then
    echo_warning "构建工具未安装，正在安装..."
    sudo apt install -y build-essential cmake
    echo_success "构建工具安装完成"
else
    echo_success "构建工具已安装"
fi

# 检查OpenCV (用于yolo-rk3588编译)
if ! pkg-config --exists opencv4; then
    echo_warning "OpenCV4未安装，正在安装..."
    sudo apt install -y libopencv-dev
    echo_success "OpenCV4安装完成"
else
    echo_success "OpenCV4已安装"
fi

# 构建ModelConverter函数
build_model_converter() {
    echo_info "开始构建ModelConverter Qt应用程序..."

    cd ModelConverter

    # 清理之前的构建
    if [ -d "build" ]; then
        echo_info "清理之前的构建..."
        rm -rf build/*
    else
        mkdir -p build
    fi

    cd build

    # 使用qmake生成Makefile
    echo_info "生成Makefile..."
    qmake ../ModelConverter.pro

    # 编译
    echo_info "编译ModelConverter..."
    make -j$(nproc)

    if [ -f "ModelConverter" ]; then
        echo_success "ModelConverter编译成功!"
        echo_info "可执行文件位置: $(pwd)/ModelConverter"

        # 设置权限
        chmod +x ModelConverter

        # 检查依赖库
        echo_info "检查依赖库..."
        ldd ModelConverter | grep -E "(Qt5|libsf-core-ls)" || true

    else
        echo_error "ModelConverter编译失败!"
        return 1
    fi

    cd "$SCRIPT_DIR"
}

# 构建Docker镜像函数
build_docker_image() {
    echo_info "开始构建Docker镜像..."

    # 检查Dockerfile
    if [ ! -f "Dockerfile" ]; then
        echo_error "未找到Dockerfile"
        return 1
    fi

    # 构建镜像
    echo_info "构建model-converter镜像 (支持ARM64)..."
    docker build -t model-converter:latest .

    if [ $? -eq 0 ]; then
        echo_success "Docker镜像构建成功!"
        echo_info "镜像名称: model-converter:latest"

        # 显示镜像信息
        docker images | grep model-converter
    else
        echo_error "Docker镜像构建失败!"
        return 1
    fi
}

# 运行YOLO模型转换函数
run_yolo_conversion() {
    echo_info "运行YOLO模型转换..."

    # 检查Docker镜像是否存在
    if ! docker images | grep -q "model-converter"; then
        echo_error "Docker镜像不存在，请先构建镜像"
        return 1
    fi

    # 示例转换命令
    echo_info "示例YOLO模型转换命令:"
    echo "docker run --rm -it \\"
    echo "  -v $(pwd)/yolo-rk3588:/workspace/yolo-rk3588 \\"
    echo "  -w /workspace/yolo-rk3588 \\"
    echo "  model-converter:latest \\"
    echo "  --model yolov8 --weight models/yolov8n.pt --dataset datasets/COCO --output result"

    read -p "是否执行示例转换? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker run --rm -it \
          -v "$(pwd)/yolo-rk3588:/workspace/yolo-rk3588" \
          -w /workspace/yolo-rk3588 \
          model-converter:latest \
          --model yolov8 --weight models/yolov8n.pt --dataset datasets/COCO --output result
    fi
}

# 编译yolo-rk3588 native版本函数
build_yolo_native() {
    echo_info "编译yolo-rk3588 native版本..."

    cd yolo-rk3588

    # 检查是否有模型文件
    if [ ! -d "models" ] || [ -z "$(ls -A models 2>/dev/null)" ]; then
        echo_warning "models目录为空，请先放入模型文件"
    fi

    # 创建输出目录
    mkdir -p deploy

    # 编译yolov5示例
    if [ -d "yolov5/cpp" ]; then
        echo_info "编译yolov5 demo..."
        ./build-linux.sh -t rk3588 -a aarch64 -d yolov5 -i "$(pwd)/result/yolov5" -o "$(pwd)/deploy"
    fi

    # 编译yolov8示例
    if [ -d "yolov8/cpp" ]; then
        echo_info "编译yolov8 demo..."
        ./build-linux.sh -t rk3588 -a aarch64 -d yolov8 -i "$(pwd)/result/yolov8" -o "$(pwd)/deploy"
    fi

    cd "$SCRIPT_DIR"
    echo_success "yolo-rk3588编译完成!"
}

# 功能选择菜单
echo_info "请选择要执行的功能:"
echo "1) 构建并启动ModelConverter Qt应用程序"
echo "2) 构建Docker镜像用于YOLO模型转换"
echo "3) 运行YOLO模型转换 (需要先构建Docker镜像)"
echo "4) 编译yolo-rk3588 native版本"
echo "5) 全部执行 (推荐)"
echo "0) 退出"

read -p "请输入选择 (0-5): " choice

case $choice in
    1)
        echo_info "构建并启动ModelConverter..."
        build_model_converter
        ;;
    2)
        echo_info "构建Docker镜像..."
        build_docker_image
        ;;
    3)
        echo_info "运行YOLO模型转换..."
        run_yolo_conversion
        ;;
    4)
        echo_info "编译yolo-rk3588 native版本..."
        build_yolo_native
        ;;
    5)
        echo_info "执行全部功能..."
        build_model_converter
        build_docker_image
        echo_success "所有组件构建完成!"
        echo_info "您现在可以:"
        echo "  - 运行 ./ModelConverter/build/ModelConverter 启动Qt应用"
        echo "  - 使用Docker进行YOLO模型转换"
        echo "  - 编译native版本的yolo-rk3588"
        ;;
    0)
        echo_info "退出"
        exit 0
        ;;
    *)
        echo_error "无效选择"
        exit 1
        ;;
esac

echo_success "脚本执行完成!"

#!/bin/bash
# ModelConverter 启动脚本 - 支持GUI和无界面模式
# 基于 terminal_command.sh 改进

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo_info "=== ModelConverter 启动脚本 ==="
echo_info "环境：Linux x86_64 + Qt5.12.6 + 许可证库"

# 解析命令行参数
GUI_MODE=true
HEADLESS_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --headless)
            HEADLESS_MODE=true
            GUI_MODE=false
            shift
            ;;
        --gui)
            GUI_MODE=true
            HEADLESS_MODE=false
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --gui       启动GUI模式 (默认)"
            echo "  --headless  启动无界面模式"
            echo "  -h, --help  显示此帮助信息"
            exit 0
            ;;
        *)
            echo_error "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 1. 检查运行环境
echo_info "1. 检查运行环境..."

# 检查是否在Linux环境中
if ! uname -a | grep -q "Linux"; then
    echo_error "此脚本需要在Linux环境中运行"
    exit 1
fi

echo_success "Linux环境检查通过"

# 2. 检查编译状态
echo_info "2. 检查程序编译状态..."

# 查找可执行文件
EXECUTABLE=""
if [ -f "build/ModelConverter" ]; then
    EXECUTABLE="build/ModelConverter"
elif [ -f "build/modelconverter" ]; then
    EXECUTABLE="build/modelconverter"
else
    echo_warning "未找到已编译的程序，开始编译..."
    
    # 检查编译工具
    if ! command -v qmake &> /dev/null; then
        echo_error "未找到qmake，请安装Qt5开发环境"
        echo_info "运行以下命令安装Qt5："
        echo_info "sudo apt install -y qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
        exit 1
    fi
    
    # 创建build目录并编译
    mkdir -p build
    cd build
    
    echo_info "Qt版本: $(qmake --version | grep Qt)"
    echo_info "正在编译Qt项目..."
    
    qmake ../ModelConverter.pro
    make -j$(nproc)
    
    if [ $? -eq 0 ]; then
        echo_success "Qt项目编译成功"
        
        # 再次查找可执行文件
        if [ -f "ModelConverter" ]; then
            EXECUTABLE="build/ModelConverter"
        elif [ -f "modelconverter" ]; then
            EXECUTABLE="build/modelconverter"
        else
            EXECUTABLE=$(find . -name "*[Mm]odel[Cc]onverter*" -type f -executable | head -1)
            if [ -n "$EXECUTABLE" ]; then
                EXECUTABLE="build/$EXECUTABLE"
            fi
        fi
    else
        echo_error "Qt项目编译失败"
        exit 1
    fi
    
    cd ..
fi

if [ -z "$EXECUTABLE" ] || [ ! -f "$EXECUTABLE" ]; then
    echo_error "未找到可执行文件"
    echo_info "build目录内容:"
    ls -la build/
    exit 1
fi

echo_success "找到可执行文件: $EXECUTABLE"

# 3. 设置运行环境
echo_info "3. 设置运行环境..."

# 创建必要的目录
mkdir -p build/models
mkdir -p build/result

# 复制许可证库文件到build目录
if [ -f "libsf-core-ls.so" ] && [ ! -f "build/libsf-core-ls.so" ]; then
    echo_info "复制许可证库文件..."
    cp libsf-core-ls.so build/
fi

# 设置库路径
export LD_LIBRARY_PATH="$(pwd)/build:$(pwd):$LD_LIBRARY_PATH"

# 设置Qt环境变量
if [ -d "/opt/Qt5.12.6/5.12.6/gcc_64" ]; then
    export LD_LIBRARY_PATH="/opt/Qt5.12.6/5.12.6/gcc_64/lib:$LD_LIBRARY_PATH"
    echo_info "使用自定义Qt路径"
else
    echo_info "使用系统Qt库"
fi

echo_success "运行环境设置完成"

# 4. GUI模式特定设置
if [ "$GUI_MODE" = true ]; then
    echo_info "4. 设置GUI环境..."
    
    # 检查显示环境
    if [ -z "$DISPLAY" ]; then
        echo_warning "未设置DISPLAY环境变量"
        
        # 尝试自动设置DISPLAY（WSL2环境）
        if grep -q microsoft /proc/version 2>/dev/null; then
            echo_info "检测到WSL2环境，自动设置DISPLAY..."
            export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
            echo_info "DISPLAY已设置为: $DISPLAY"
            echo_warning "请确保Windows中的VcXsrv X11服务器正在运行"
        else
            echo_warning "请手动设置DISPLAY环境变量"
            echo_info "例如: export DISPLAY=:0"
        fi
    else
        echo_success "DISPLAY环境变量已设置: $DISPLAY"
    fi
    
    # 设置Qt字体和插件环境变量
    export QT_QPA_FONTDIR="/usr/share/fonts"
    export FONTCONFIG_PATH="/etc/fonts"
    export QT_QPA_PLATFORM_PLUGIN_PATH="/usr/lib/x86_64-linux-gnu/qt5/plugins"
    
    # 测试X11连接
    echo_info "测试X11连接..."
    if command -v xeyes &> /dev/null; then
        timeout 3 xeyes &>/dev/null &
        if [ $? -eq 0 ]; then
            echo_success "X11连接正常"
        else
            echo_warning "X11连接可能有问题，但继续尝试启动GUI"
        fi
    else
        echo_info "xeyes未安装，跳过X11测试"
    fi
    
    echo_success "GUI环境设置完成"
fi

# 5. 检查程序依赖
echo_info "5. 检查程序依赖..."
cd build

echo_info "检查动态库依赖..."
if ldd "$EXECUTABLE" | grep "not found"; then
    echo_warning "发现缺失的依赖库，但继续尝试运行"
else
    echo_success "所有依赖库都可用"
fi

echo_info "Qt和许可证库依赖:"
ldd "$EXECUTABLE" | grep -E "(Qt5|libsf-core-ls)" || echo_info "未显示Qt5或许可证库依赖"

# 6. 启动程序
echo_info "6. 启动ModelConverter..."

if [ "$GUI_MODE" = true ]; then
    echo_info "启动GUI模式..."
    echo_info "激活参数（测试用）："
    echo_info "  设备号: 50bf70582c5ea2ac7502656f8cfb522e"
    echo_info "  产品号: 40201"
    echo_info "  激活码: E76G-JEQR-EQRA-T7ZW"
    echo_info ""
    echo_info "程序启动后："
    echo_info "1. 可以启用调试模式进行测试"
    echo_info "2. 或输入真实激活码进行激活"
    echo_info "3. 激活成功后即可使用模型转换功能"
    echo_info ""
    
    # 启动GUI程序
    ./"$(basename "$EXECUTABLE")"
else
    echo_info "启动无界面模式..."
    echo_info "无界面模式下仅支持命令行操作"
    echo_info "可以通过脚本参数或配置文件进行模型转换"
    
    # 这里可以添加无界面模式的逻辑
    echo_warning "无界面模式功能待实现"
    echo_info "当前仅支持GUI模式，请使用 --gui 参数"
fi

echo_success "程序启动完成"

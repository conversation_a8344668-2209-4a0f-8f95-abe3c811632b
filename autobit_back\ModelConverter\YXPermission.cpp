// Linux兼容的YXPermission实现
#include "YXPermission.h"
#include <QCryptographicHash>
#include <QtCore/QtCore>
#include <QProcess>
#include <QDebug>
#include "SFCoreIntf.h"

// Linux标准头文件
#include <stdio.h>
#include <locale.h>
#include <fstream>
#include <iostream>
#include <time.h>
#include <unistd.h>
#include <sys/stat.h>
#include <string>

const char* g_salt = "ABCDEF!@#$%^";

//////////////////////////////////////////////////////////////////////////
// Linux兼容的字符串转换函数
std::wstring StringToWString(const std::string& str)
{
    // 在Linux中，使用Qt的转换功能
    QString qstr = QString::fromStdString(str);
    return qstr.toStdWString();
}

std::string WStringToString(const std::wstring &wstr)
{
    // 在Linux中，使用Qt的转换功能
    QString qstr = QString::fromStdWString(wstr);
    return qstr.toStdString();
}

std::string GetMachineGUID()
{
    // Linux版本：生成基于系统信息的唯一标识
    std::string machineId;
    
    // 尝试读取machine-id
    std::ifstream machineIdFile("/etc/machine-id");
    if (machineIdFile.is_open()) {
        std::getline(machineIdFile, machineId);
        machineIdFile.close();
        if (!machineId.empty()) {
            return machineId;
        }
    }
    
    // 备用方案：使用dbus machine-id
    std::ifstream dbusIdFile("/var/lib/dbus/machine-id");
    if (dbusIdFile.is_open()) {
        std::getline(dbusIdFile, machineId);
        dbusIdFile.close();
        if (!machineId.empty()) {
            return machineId;
        }
    }
    
    // 最后备用方案：基于主机名和其他系统信息生成
    QProcess process;
    process.start("hostname");
    process.waitForFinished();
    QString hostname = process.readAllStandardOutput().trimmed();
    
    if (!hostname.isEmpty()) {
        QCryptographicHash hash(QCryptographicHash::Md5);
        hash.addData(hostname.toUtf8());
        hash.addData("LINUX_MACHINE_GUID_SALT");
        return hash.result().toHex().toStdString();
    }
    
    return "LINUX_DEFAULT_MACHINE_GUID";
}

// Linux兼容的设备信息获取函数
bool GetDeviceInfoByCmd(char *&lpszResult, const std::string& cmdLine, const std::string& key)
{
    // Linux版本：使用QProcess执行命令并解析输出
    const int COMMAND_SIZE = 1020;
    
    lpszResult = (char*)malloc((COMMAND_SIZE + 1) * sizeof(char));
    memset(lpszResult, 0x00, (COMMAND_SIZE + 1) * sizeof(char));
    
    try {
        QProcess process;
        process.start("bash", QStringList() << "-c" << QString::fromStdString(cmdLine));
        process.waitForFinished(5000); // 5秒超时
        
        QString output = process.readAllStandardOutput();
        std::string strBuffer = output.toStdString();
        
        // 查找关键字
        size_t pos = strBuffer.find(key);
        if (pos == std::string::npos) {
            // 未找到关键字，返回默认值
            strcpy(lpszResult, "LINUX_DEFAULT_VALUE");
            return false;
        }
        
        // 提取关键字后的内容
        strBuffer = strBuffer.substr(pos + key.length());
        
        // 清理空格、换行符等
        std::string cleanResult;
        for (char c : strBuffer) {
            if (c != ' ' && c != '\n' && c != '\r' && c != '\t') {
                cleanResult += c;
                if (cleanResult.length() >= COMMAND_SIZE - 1) break;
            }
        }
        
        // 复制结果
        strncpy(lpszResult, cleanResult.c_str(), COMMAND_SIZE - 1);
        lpszResult[COMMAND_SIZE] = '\0';
        
        return !cleanResult.empty();
        
    } catch (...) {
        // 异常处理
        strcpy(lpszResult, "LINUX_ERROR_VALUE");
        return false;
    }
}

// 获取设备序列号的Linux实现
std::string getDeviceSerial()
{
    // 尝试多种方法获取设备序列号
    std::string serial;
    
    // 方法1: DMI产品序列号
    std::ifstream serialFile("/sys/class/dmi/id/product_serial");
    if (serialFile.is_open()) {
        std::getline(serialFile, serial);
        serialFile.close();
        if (!serial.empty() && serial != "Not Specified") {
            return serial;
        }
    }
    
    // 方法2: 主板序列号
    std::ifstream boardFile("/sys/class/dmi/id/board_serial");
    if (boardFile.is_open()) {
        std::getline(boardFile, serial);
        boardFile.close();
        if (!serial.empty() && serial != "Not Specified") {
            return serial;
        }
    }
    
    // 方法3: 使用machine-id作为备用
    return GetMachineGUID();
}

// 主要的设备号获取函数 - 这是许可证库需要的
std::string getDeviceNo()
{
    // 生成基于多种硬件信息的设备号
    std::string deviceInfo;
    
    // 收集各种设备信息
    deviceInfo += GetMachineGUID();
    deviceInfo += getDeviceSerial();
    
    // 使用MD5生成最终的设备号
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(deviceInfo.c_str());
    hash.addData(g_salt);
    
    std::string deviceNo = hash.result().toHex().toStdString();
    
    qDebug() << "[YXPermission] Linux设备号:" << QString::fromStdString(deviceNo);
    
    return deviceNo;
}

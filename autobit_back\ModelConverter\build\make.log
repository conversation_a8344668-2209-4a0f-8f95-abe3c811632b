g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o main.o ../main.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o activationwindow.o ../activationwindow.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o conversionwindow.o ../conversionwindow.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o LicenseManager.o ../LicenseManager.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o YXPermission.o ../YXPermission.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o sized_delete.o ../sized_delete.cpp
/opt/Qt5.12.6/5.12.6/gcc_64/bin/rcc -name ModelConverter ../ModelConverter.qrc -o qrc_ModelConverter.cpp
g++ -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -dM -E -o moc_predefs.h /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp
/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --include /mnt/e/code/autobit_back/ModelConverter/build/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/autobit_back/ModelConverter -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../activationwindow.h -o moc_activationwindow.cpp
/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --include /mnt/e/code/autobit_back/ModelConverter/build/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/autobit_back/ModelConverter -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../conversionwindow.h -o moc_conversionwindow.cpp
/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --include /mnt/e/code/autobit_back/ModelConverter/build/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/autobit_back/ModelConverter -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../LicenseManager.h -o moc_LicenseManager.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o qrc_ModelConverter.o qrc_ModelConverter.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o moc_LicenseManager.o moc_LicenseManager.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o moc_conversionwindow.o moc_conversionwindow.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o moc_activationwindow.o moc_activationwindow.cpp
In file included from ../YXPermission.cpp:2:
../YXPermission.h:26:29: error: ‘QString’ has not been declared
   26 |     static bool licenseInfo(QString &storeNo, QString &license);
      |                             ^~~~~~~
../YXPermission.h:26:47: error: ‘QString’ has not been declared
   26 |     static bool licenseInfo(QString &storeNo, QString &license);
      |                                               ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant:1,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h:47,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h:44,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager:1,
                 from ../LicenseManager.h:6,
                 from ../LicenseManager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h:47,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h:52,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication:1,
                 from ../main.cpp:2:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QtCore:10,
                 from ../YXPermission.cpp:4:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:44,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../conversionwindow.h:6,
                 from ../conversionwindow.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../conversionwindow.h:6,
                 from ../conversionwindow.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h:48,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h:46,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit:1,
                 from ../activationwindow.h:5,
                 from ../activationwindow.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant:1,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h:47,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h:44,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager:1,
                 from ../LicenseManager.h:6,
                 from moc_LicenseManager.cpp:9:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../activationwindow.h:14,
                 from ../main.cpp:3:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../activationwindow.h:14,
                 from ../activationwindow.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:44,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../conversionwindow.h:6,
                 from moc_conversionwindow.cpp:9:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../conversionwindow.h:6,
                 from moc_conversionwindow.cpp:9:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h:48,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h:46,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit:1,
                 from ../activationwindow.h:5,
                 from moc_activationwindow.cpp:9:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../activationwindow.h:14,
                 from moc_activationwindow.cpp:9:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
make: *** [Makefile:2270: YXPermission.o] Error 1
make: *** Waiting for unfinished jobs....

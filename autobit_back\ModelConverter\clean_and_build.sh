#!/bin/bash
# 清理并重新编译脚本

echo "=== 清理并重新编译 ModelConverter ==="

# 进入项目目录
cd "$(dirname "$0")"

# 清理build目录
echo "清理build目录..."
rm -rf build/*

# 重新创建build目录
mkdir -p build
cd build

# 复制许可证库
if [ -f "../libsf-core-ls.so" ]; then
    echo "复制许可证库..."
    cp ../libsf-core-ls.so .
fi

# 生成Makefile
echo "生成Makefile..."
qmake ../ModelConverter.pro

if [ $? -eq 0 ]; then
    echo "✅ qmake成功"
    
    # 编译
    echo "开始编译..."
    make -j$(nproc) 2>&1 | tee build.log
    
    if [ $? -eq 0 ]; then
        echo "🎉 编译成功!"
        
        # 查找可执行文件
        EXECUTABLE=""
        if [ -f "ModelConverter" ]; then
            EXECUTABLE="ModelConverter"
        elif [ -f "modelconverter" ]; then
            EXECUTABLE="modelconverter"
        else
            EXECUTABLE=$(find . -name "*[Mm]odel[Cc]onverter*" -type f -executable | head -1)
        fi
        
        if [ -n "$EXECUTABLE" ]; then
            echo "✅ 可执行文件: $EXECUTABLE"
            ls -la "$EXECUTABLE"
            file "$EXECUTABLE"
            
            # 检查依赖
            echo "依赖库检查:"
            ldd "$EXECUTABLE" | grep -E "(Qt5|libsf-core-ls)" || echo "无特殊依赖"
            
            echo ""
            echo "🚀 编译完成！可以运行程序了："
            echo "   cd build"
            echo "   export LD_LIBRARY_PATH=\"/opt/Qt5.12.6/5.12.6/gcc_64/lib:.:$LD_LIBRARY_PATH\""
            echo "   ./$EXECUTABLE"
            
        else
            echo "❌ 未找到可执行文件"
            echo "build目录内容:"
            ls -la
        fi
        
    else
        echo "❌ 编译失败!"
        echo "错误信息:"
        tail -20 build.log
    fi
    
else
    echo "❌ qmake失败!"
fi

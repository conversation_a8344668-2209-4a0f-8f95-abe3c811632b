#############################################################################
# Makefile for building: ModelConverter
# Generated by qmake (3.1) (Qt 5.12.6)
# Project:  ../ModelConverter.pro
# Template: app
# Command: /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake -o Makefile ../ModelConverter.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DUSE_LICENSE_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../../ModelConverter -I. -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++
QMAKE         = /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake -install qinstall
QINSTALL_PROGRAM = /opt/Qt5.12.6/5.12.6/gcc_64/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = ModelConverter1.0.0
DISTDIR = /mnt/e/code/autobit_back/ModelConverter/build/.tmp/ModelConverter1.0.0
LINK          = g++
LFLAGS        = -Wl,-O1 -Wl,-rpath,/opt/Qt5.12.6/5.12.6/gcc_64/lib -Wl,-rpath,/mnt/e/code/autobit_back/ModelConverter -Wl,-rpath,/opt/Qt5.12.6/5.12.6/gcc_64/lib
LIBS          = $(SUBLIBS) -L/opt/Qt5.12.6/5.12.6/gcc_64/lib -lQt5Widgets -L/home/<USER>/openssl-1.1.1b/lib -lQt5Gui -lQt5Core -L/mnt/e/code/autobit_back/ModelConverter -lsf-core-ls /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Widgets.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Gui.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Network.so /opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Core.so -lGL -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../main.cpp \
		../activationwindow.cpp \
		../conversionwindow.cpp \
		../LicenseManager.cpp \
		../YXPermission.cpp \
		../sized_delete.cpp qrc_ModelConverter.cpp \
		moc_activationwindow.cpp \
		moc_conversionwindow.cpp \
		moc_LicenseManager.cpp
OBJECTS       = main.o \
		activationwindow.o \
		conversionwindow.o \
		LicenseManager.o \
		YXPermission.o \
		sized_delete.o \
		qrc_ModelConverter.o \
		moc_activationwindow.o \
		moc_conversionwindow.o \
		moc_LicenseManager.o
DIST          = /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/linux.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/sanitize.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/qconfig.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_functions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_post.prf \
		../../.qmake.stash \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/toolchain.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resolve_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_post.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/warn_on.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resources.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/moc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/uic.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/thread.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qmake_use.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/file_copies.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exceptions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/yacc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/lex.prf \
		../../ModelConverter.pro ../activationwindow.h \
		../conversionwindow.h \
		../LicenseManager.h \
		../YXPermission.h \
		../SFCoreIntf.h \
		../yxcppsdkdishcore_global.h ../main.cpp \
		../activationwindow.cpp \
		../conversionwindow.cpp \
		../LicenseManager.cpp \
		../YXPermission.cpp \
		../sized_delete.cpp
QMAKE_TARGET  = ModelConverter
DESTDIR       = 
TARGET        = ModelConverter


first: all
####### Build rules

ModelConverter:  $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../ModelConverter.pro /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/linux.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/sanitize.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-base.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-unix.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/qconfig.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_functions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_post.prf \
		../.qmake.stash \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/toolchain.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_pre.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resolve_config.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_post.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/warn_on.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resources.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/moc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/uic.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/thread.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qmake_use.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/file_copies.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exceptions.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/yacc.prf \
		/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/lex.prf \
		../ModelConverter.pro \
		../ModelConverter.qrc \
		/opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Widgets.prl \
		/opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Gui.prl \
		/opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Core.prl
	$(QMAKE) -o Makefile ../ModelConverter.pro
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_pre.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/unix.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/linux.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/sanitize.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-base.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/common/g++-unix.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/qconfig.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_functions.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt_config.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/spec_post.prf:
../.qmake.stash:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/toolchain.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_pre.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resolve_config.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/default_post.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/warn_on.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qt.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/resources.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/moc.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/uic.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/unix/thread.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/qmake_use.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/file_copies.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/exceptions.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/yacc.prf:
/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/lex.prf:
../ModelConverter.pro:
../ModelConverter.qrc:
/opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Widgets.prl:
/opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Gui.prl:
/opt/Qt5.12.6/5.12.6/gcc_64/lib/libQt5Core.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ../ModelConverter.pro

qmake_all: FORCE


all: Makefile ModelConverter

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../ModelConverter.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../activationwindow.h ../conversionwindow.h ../LicenseManager.h ../YXPermission.h ../SFCoreIntf.h ../yxcppsdkdishcore_global.h $(DISTDIR)/
	$(COPY_FILE) --parents ../main.cpp ../activationwindow.cpp ../conversionwindow.cpp ../LicenseManager.cpp ../YXPermission.cpp ../sized_delete.cpp $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_ModelConverter.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_ModelConverter.cpp
qrc_ModelConverter.cpp: ../ModelConverter.qrc \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/rcc \
		../icons/error.png \
		../icons/working.png \
		../icons/ready.png \
		../icons/success.png
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/rcc -name ModelConverter ../ModelConverter.qrc -o qrc_ModelConverter.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp
	g++ -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -dM -E -o moc_predefs.h /opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_activationwindow.cpp moc_conversionwindow.cpp moc_LicenseManager.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_activationwindow.cpp moc_conversionwindow.cpp moc_LicenseManager.cpp
moc_activationwindow.cpp: ../activationwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGraphicsDropShadowEffect \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgraphicseffect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QPropertyAnimation \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpropertyanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariantanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeasingcurve.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QCheckBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcheckbox.h \
		../conversionwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QSplitter \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsplitter.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTabWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QListWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qitemselectionmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		../LicenseManager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSharedDataPointer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVector \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslConfiguration \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtcpsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qabstractsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslerror.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslcertificate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcryptographichash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qssl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFlags \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMetaType \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkReply \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkreply.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QIODevice \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkRequest \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCryptographicHash \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		../SFCoreIntf.h \
		moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/autobit_back/ModelConverter/build/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/autobit_back/ModelConverter -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../activationwindow.h -o moc_activationwindow.cpp

moc_conversionwindow.cpp: ../conversionwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QSplitter \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsplitter.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTabWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QListWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qitemselectionmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QPropertyAnimation \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpropertyanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariantanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeasingcurve.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/autobit_back/ModelConverter/build/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/autobit_back/ModelConverter -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../conversionwindow.h -o moc_conversionwindow.cpp

moc_LicenseManager.cpp: ../LicenseManager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSharedDataPointer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVector \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslConfiguration \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtcpsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qabstractsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslerror.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslcertificate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcryptographichash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qssl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFlags \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMetaType \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkReply \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkreply.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QIODevice \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkRequest \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCryptographicHash \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		moc_predefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc
	/opt/Qt5.12.6/5.12.6/gcc_64/bin/moc $(DEFINES) --include /mnt/e/code/autobit_back/ModelConverter/build/moc_predefs.h -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -I/mnt/e/code/autobit_back/ModelConverter -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../LicenseManager.h -o moc_LicenseManager.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean 

####### Compile

main.o: ../main.cpp /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		../activationwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGraphicsDropShadowEffect \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgraphicseffect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QPropertyAnimation \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpropertyanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariantanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeasingcurve.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QCheckBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcheckbox.h \
		../conversionwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QSplitter \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsplitter.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTabWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QListWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qitemselectionmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		../LicenseManager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSharedDataPointer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVector \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslConfiguration \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtcpsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qabstractsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslerror.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslcertificate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcryptographichash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qssl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFlags \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMetaType \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkReply \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkreply.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QIODevice \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkRequest \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCryptographicHash \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		../SFCoreIntf.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../main.cpp

activationwindow.o: ../activationwindow.cpp ../activationwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGraphicsDropShadowEffect \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgraphicseffect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QPropertyAnimation \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpropertyanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariantanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeasingcurve.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QCheckBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcheckbox.h \
		../conversionwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QSplitter \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsplitter.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTabWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QListWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qitemselectionmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		../LicenseManager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSharedDataPointer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVector \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslConfiguration \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtcpsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qabstractsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslerror.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslcertificate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcryptographichash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qssl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFlags \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMetaType \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkReply \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkreply.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QIODevice \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkRequest \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCryptographicHash \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		../SFCoreIntf.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QDesktopWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QScreen \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qscreen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QList \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QRect \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSize \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSizeF \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QTransform \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkInterface \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qhostaddress.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSysInfo \
		../YXPermission.h \
		../yxcppsdkdishcore_global.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o activationwindow.o ../activationwindow.cpp

conversionwindow.o: ../conversionwindow.cpp ../conversionwindow.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpalette.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcolor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgb.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qbrush.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qregion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtransform.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfont.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qicon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qpen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QLabel \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QProgressBar \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qprogressbar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFrame \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGroupBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgroupbox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QGridLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QSplitter \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qsplitter.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTabWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QListWidget \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qlistview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qitemselectionmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QPropertyAnimation \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpropertyanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariantanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeasingcurve.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QMovie \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qmovie.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimagereader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qimageiohandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QDesktopServices \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qdesktopservices.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCoreApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFileInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDateTime \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QApplication \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QScreen \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qscreen.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QList \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QRect \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSize \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSizeF \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/QTransform
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o conversionwindow.o ../conversionwindow.cpp

LicenseManager.o: ../LicenseManager.cpp ../LicenseManager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QString \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkAccessManager \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkaccessmanager.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkrequest.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSharedDataPointer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUrl \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVariant \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QVector \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslConfiguration \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qtcpsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qabstractsocket.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslerror.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslcertificate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcryptographichash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qssl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QFlags \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QMetaType \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkReply \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkreply.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QIODevice \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkRequest \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonDocument \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCryptographicHash \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSettings \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QJsonArray \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QSysInfo \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/QNetworkInterface \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qnetworkinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork/qhostaddress.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QProcess \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStandardPaths \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDir \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QTimer \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QUuid \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/quuid.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o LicenseManager.o ../LicenseManager.cpp

YXPermission.o: ../YXPermission.cpp ../yxPermission.h \
		../yxcppsdkdishcore_global.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QCryptographicHash \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcryptographichash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobal.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qconfig.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlogging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qflags.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmutex.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QtCore \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QtCoreDepends \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstring.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qchar.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringview.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpair.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregexp.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstracteventdispatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhash.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpoint.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qset.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractnativeeventfilter.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractproxymodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstractstate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qabstracttransition.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qanimationgroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydataops.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qarraydatapointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbasictimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbitarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbuffer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qbytearraymatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcache.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcborarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcborvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatetime.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcborcommon.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurl.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/quuid.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcbormap.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcborstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfloat16.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcollator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcommandlineoption.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcommandlineparser.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdeadlinetimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qelapsedtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdiriterator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeasingcurve.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qendian.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qeventtransition.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qexception.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfactoryinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfileselector.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QStringList \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfilesystemwatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfinalstate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfuture.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfutureinterface.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrunnable.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qresultstore.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfuturesynchronizer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qfuturewatcher.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qhistorystate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qidentityproxymodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qisenum.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qitemselectionmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonarray.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonvalue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsondocument.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qjsonobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlibrary.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlibraryinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qversionnumber.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlinkedlist.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlockfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qloggingcategory.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmargins.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmath.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmetaobject.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmimedata.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmimedatabase.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qmimetype.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobjectcleanuphandler.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qoperatingsystemversion.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qparallelanimationgroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpauseanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qplugin.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpointer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpluginloader.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qprocess.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qpropertyanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariantanimation.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qqueue.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrandom.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qreadwritelock.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qrect.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsize.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qresource.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsavefile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopedvaluerollback.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qscopeguard.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsemaphore.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsequentialanimationgroup.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsettings.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsharedmemory.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsignalmapper.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsignaltransition.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsocketnotifier.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsortfilterproxymodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstack.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstate.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstatemachine.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstorageinfo.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlistmodel.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qsystemsemaphore.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtemporarydir.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtemporaryfile.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextboundaryfinder.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextcodec.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthread.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthreadpool.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qthreadstorage.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimeline.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimer.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtimezone.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtranslator.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtypetraits.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qwaitcondition.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qwineventnotifier.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qxmlstream.h \
		/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtcoreversion.h \
		../SFCoreIntf.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o YXPermission.o ../YXPermission.cpp

sized_delete.o: ../sized_delete.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o sized_delete.o ../sized_delete.cpp

qrc_ModelConverter.o: qrc_ModelConverter.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_ModelConverter.o qrc_ModelConverter.cpp

moc_activationwindow.o: moc_activationwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_activationwindow.o moc_activationwindow.cpp

moc_conversionwindow.o: moc_conversionwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_conversionwindow.o moc_conversionwindow.cpp

moc_LicenseManager.o: moc_LicenseManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_LicenseManager.o moc_LicenseManager.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:


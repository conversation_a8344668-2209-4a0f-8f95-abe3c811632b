# autobit_back ARM64 Debian 部署指南

本项目已经适配ARM64架构的Debian系统。以下是完整的部署和使用指南。

## 系统要求

- **操作系统**: Debian 11/12 (Bullseye/Bookworm) ARM64
- **架构**: aarch64 (ARM64)
- **内存**: 建议4GB以上
- **存储**: 建议20GB以上可用空间
- **网络**: 需要互联网连接下载依赖

## 快速开始

### 1. 克隆项目并进入目录
```bash
cd autobit_back
```

### 2. 运行自动化部署脚本
```bash
# 给脚本添加执行权限
chmod +x start_autobit_back.sh

# 运行部署脚本
./start_autobit_back.sh
```

### 3. 选择功能
脚本会提供以下选项：
- `1` - 构建并启动ModelConverter Qt应用程序
- `2` - 构建Docker镜像用于YOLO模型转换  
- `3` - 运行YOLO模型转换
- `4` - 编译yolo-rk3588 native版本
- `5` - 全部执行 (推荐)

## 手动部署步骤

如果自动化脚本遇到问题，可以按照以下步骤手动部署：

### 1. 安装系统依赖

```bash
# 更新包管理器
sudo apt update

# 安装基础构建工具
sudo apt install -y build-essential cmake git wget curl

# 安装Qt5开发库
sudo apt install -y qtbase5-dev qt5-qmake qtbase5-dev-tools \
                    libqt5widgets5 libqt5gui5 libqt5core5a

# 安装OpenCV (用于yolo-rk3588)
sudo apt install -y libopencv-dev

# 安装Docker
sudo apt install -y docker.io
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER
```

### 2. 构建ModelConverter Qt应用

```bash
cd ModelConverter
mkdir -p build
cd build

# 生成Makefile
qmake ../ModelConverter.pro

# 编译
make -j$(nproc)

# 检查编译结果
ls -la ModelConverter
```

### 3. 构建Docker镜像

```bash
cd ../../  # 回到项目根目录

# 构建支持ARM64的Docker镜像
docker build -t model-converter:latest .
```

### 4. 编译yolo-rk3588 native版本

```bash
cd yolo-rk3588

# 编译yolov5 demo (如果有模型文件)
./build-linux.sh -t rk3588 -a aarch64 -d yolov5 \
  -i "$(pwd)/result/yolov5" -o "$(pwd)/deploy"

# 编译yolov8 demo (如果有模型文件)  
./build-linux.sh -t rk3588 -a aarch64 -d yolov8 \
  -i "$(pwd)/result/yolov8" -o "$(pwd)/deploy"
```

## 使用说明

### ModelConverter Qt应用

```bash
cd ModelConverter/build

# 设置环境变量
export LD_LIBRARY_PATH="/usr/lib/aarch64-linux-gnu/qt5/lib:${LD_LIBRARY_PATH}"

# 启动应用
./ModelConverter
```

### YOLO模型转换 (Docker方式)

```bash
# 进入Docker环境进行模型转换
docker run --rm -it \
  -v "$(pwd)/yolo-rk3588:/workspace/yolo-rk3588" \
  -w /workspace/yolo-rk3588 \
  model-converter:latest \
  --model yolov8 --weight models/yolov8n.pt --dataset datasets/COCO --output result
```

### Native编译的YOLO推理

```bash
cd yolo-rk3588/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo

# 设置库路径
export LD_LIBRARY_PATH=./lib

# 运行推理
./rknn_yolov8_demo model/yolov8n.rknn model/test_image.jpg
```

## 架构适配说明

本项目已针对ARM64架构进行以下适配：

### 1. Docker镜像适配
- 自动检测架构并下载对应的Miniconda版本
- 支持ARM64和x86_64双架构

### 2. 编译工具链适配  
- ARM64主机使用native编译器 (gcc/g++)
- x86_64主机使用交叉编译工具链
- 自动检测并选择合适的编译方式

### 3. Qt库路径适配
- ARM64系统使用系统Qt库 (`/usr/lib/aarch64-linux-gnu/qt5/`)
- x86_64系统使用自定义Qt路径
- 自动设置正确的库路径和rpath

### 4. 依赖库适配
- 使用系统包管理器安装ARM64版本的依赖
- 自动处理架构相关的库路径

## 故障排除

### 1. Qt应用无法启动
```bash
# 检查Qt库是否正确安装
dpkg -l | grep qt5

# 检查库路径
ldd ModelConverter/build/ModelConverter

# 重新安装Qt开发库
sudo apt install --reinstall qtbase5-dev
```

### 2. Docker构建失败
```bash
# 检查Docker服务状态
sudo systemctl status docker

# 清理Docker缓存
docker system prune -a

# 重新构建镜像
docker build --no-cache -t model-converter:latest .
```

### 3. 编译错误
```bash
# 检查编译器版本
gcc --version
g++ --version

# 安装缺失的开发库
sudo apt install -y build-essential cmake libopencv-dev
```

### 4. 权限问题
```bash
# 添加用户到docker组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker

# 设置脚本执行权限
chmod +x start_autobit_back.sh
```

## 性能优化建议

1. **内存优化**: 确保系统有足够内存，建议4GB以上
2. **存储优化**: 使用SSD存储以提高编译和Docker构建速度
3. **并行编译**: 使用 `make -j$(nproc)` 进行并行编译
4. **Docker优化**: 使用多阶段构建减少镜像大小

## 支持的功能

- ✅ ModelConverter Qt应用程序 (ARM64 native)
- ✅ YOLO模型转换 (Docker + ARM64)
- ✅ yolo-rk3588编译 (ARM64 native)
- ✅ 自动依赖检查和安装
- ✅ 多架构支持 (ARM64/x86_64)

## 联系支持

如果遇到问题，请检查：
1. 系统架构是否为ARM64 (`uname -m` 应显示 `aarch64`)
2. 所有依赖是否正确安装
3. Docker服务是否正常运行
4. 用户是否有足够权限

更多技术细节请参考项目源码和注释。

// 简化版本的转换界面 - 恢复原始设计
#include "conversionwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QFileDialog>
#include <QMessageBox>
#include <QDir>
#include <QFileInfo>
#include <QTimer>
#include <QDebug>

ConversionWindow::ConversionWindow(QWidget *parent)
    : QWidget(parent), process(nullptr), progressTimer(new QTimer(this))
{
    setupUI();
    setupConnections();
    setupStyles();
    
    // 设置进度定时器
    progressTimer->setSingleShot(false);
    connect(progressTimer, &QTimer::timeout, this, &ConversionWindow::updateProgress);
}

void ConversionWindow::setupUI()
{
    setWindowTitle("模型转换器 - 主界面");
    setMinimumSize(800, 600);

    // 主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(20);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 标题
    QLabel *titleLabel = new QLabel("YOLO模型转换工具", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);

    // 输入区域
    QGroupBox *inputGroup = new QGroupBox("模型配置", this);
    inputGroup->setObjectName("inputGroup");

    // 模型类型选择
    modelTypeCombo = new QComboBox(this);
    modelTypeCombo->setObjectName("modelTypeCombo");
    modelTypeCombo->addItems({"yolov5", "yolov8"});  // 恢复原始的简单选择

    // 模型文件选择
    modelPathEdit = new QLineEdit(this);
    modelPathEdit->setObjectName("modelPathEdit");
    modelPathEdit->setPlaceholderText("选择模型文件 (.pt, .onnx)");

    browseButton = new QPushButton("📁 浏览", this);
    browseButton->setObjectName("browseButton");

    // 转换按钮
    convertButton = new QPushButton("🚀 转换", this);
    convertButton->setObjectName("convertButton");
    convertButton->setDefault(true);

    // 输入组布局
    QGridLayout *inputLayout = new QGridLayout(inputGroup);
    inputLayout->addWidget(new QLabel("模型类型:"), 0, 0);
    inputLayout->addWidget(modelTypeCombo, 0, 1);
    inputLayout->addWidget(new QLabel("模型文件:"), 1, 0);
    inputLayout->addWidget(modelPathEdit, 1, 1);
    inputLayout->addWidget(browseButton, 1, 2);
    inputLayout->addWidget(convertButton, 2, 1);

    mainLayout->addWidget(inputGroup);

    // 输出区域
    QGroupBox *outputGroup = new QGroupBox("转换输出", this);
    outputGroup->setObjectName("outputGroup");

    // 结果显示
    resultDisplay = new QTextEdit(this);
    resultDisplay->setObjectName("resultDisplay");
    resultDisplay->setReadOnly(true);
    resultDisplay->setMinimumHeight(300);

    // 状态显示
    statusLabel = new QLabel("等待转换", this);
    statusLabel->setObjectName("statusLabel");
    statusLabel->setAlignment(Qt::AlignCenter);

    // 进度条
    progressBar = new QProgressBar(this);
    progressBar->setObjectName("progressBar");
    progressBar->setVisible(false);

    // 输出组布局
    QVBoxLayout *outputLayout = new QVBoxLayout(outputGroup);
    outputLayout->addWidget(resultDisplay);
    outputLayout->addWidget(statusLabel);
    outputLayout->addWidget(progressBar);

    mainLayout->addWidget(outputGroup);

    // 操作按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    clearLogButton = new QPushButton("🗑️ 清空日志", this);
    clearLogButton->setObjectName("clearLogButton");
    
    openResultButton = new QPushButton("📂 打开结果", this);
    openResultButton->setObjectName("openResultButton");
    
    buttonLayout->addWidget(clearLogButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(openResultButton);
    
    mainLayout->addLayout(buttonLayout);
}

void ConversionWindow::setupConnections()
{
    connect(browseButton, &QPushButton::clicked, this, &ConversionWindow::onBrowseClicked);
    connect(convertButton, &QPushButton::clicked, this, &ConversionWindow::onConvertClicked);
    connect(clearLogButton, &QPushButton::clicked, this, &ConversionWindow::onClearLogClicked);
    connect(openResultButton, &QPushButton::clicked, this, &ConversionWindow::onOpenResultClicked);
}

void ConversionWindow::setupStyles()
{
    setStyleSheet(R"(
        QWidget {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
            font-size: 12px;
        }
        
        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            color: #34495e;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            background-color: #f5f5f5;
        }
        
        QComboBox, QLineEdit {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
            font-size: 12px;
        }
        
        QComboBox:focus, QLineEdit:focus {
            border-color: #3498db;
        }
        
        QPushButton {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }
        
        #convertButton {
            background-color: #27ae60;
            color: white;
            font-size: 14px;
            padding: 12px 30px;
        }
        
        #convertButton:hover {
            background-color: #2ecc71;
        }
        
        #convertButton:pressed {
            background-color: #229954;
        }
        
        #convertButton:disabled {
            background-color: #95a5a6;
        }
        
        #browseButton {
            background-color: #3498db;
            color: white;
        }
        
        #browseButton:hover {
            background-color: #5dade2;
        }
        
        #clearLogButton {
            background-color: #e74c3c;
            color: white;
        }
        
        #clearLogButton:hover {
            background-color: #ec7063;
        }
        
        #openResultButton {
            background-color: #f39c12;
            color: white;
        }
        
        #openResultButton:hover {
            background-color: #f7dc6f;
        }
        
        QTextEdit {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 11px;
        }
        
        #statusLabel {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            border-radius: 4px;
            background-color: #ecf0f1;
            color: #2c3e50;
        }
        
        QProgressBar {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        
        QProgressBar::chunk {
            background-color: #3498db;
            border-radius: 2px;
        }
    )");
}

void ConversionWindow::onBrowseClicked()
{
    QString filePath = QFileDialog::getOpenFileName(
        this,
        "选择模型文件",
        QDir::homePath(),
        "模型文件 (*.pt *.onnx *.pth);;PyTorch模型 (*.pt *.pth);;ONNX模型 (*.onnx);;所有文件 (*.*)"
    );

    if (!filePath.isEmpty()) {
        modelPathEdit->setText(filePath);

        // 自动检测模型类型
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName().toLower();

        if (fileName.contains("yolov5")) {
            modelTypeCombo->setCurrentText("yolov5");
        } else if (fileName.contains("yolov8")) {
            modelTypeCombo->setCurrentText("yolov8");
        }

        addLogMessage("已选择模型文件: " + filePath, "info");
        setConversionStatus("模型文件已选择，可以开始转换", "info");
    }
}

void ConversionWindow::onConvertClicked()
{
    QString modelPath = modelPathEdit->text().trimmed();
    if (modelPath.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先选择模型文件！");
        return;
    }

    if (!QFile::exists(modelPath)) {
        QMessageBox::critical(this, "错误", "模型文件不存在！");
        return;
    }

    // 开始转换
    convertButton->setEnabled(false);
    convertButton->setText("⏳ 转换中...");
    progressBar->setVisible(true);
    progressTimer->start(100);

    setConversionStatus("正在准备转换环境...", "working");
    addLogMessage("开始模型转换", "info");

    QString modelType = modelTypeCombo->currentText();
    QString fileName = QFileInfo(modelPath).fileName();

    addLogMessage(QString("模型类型: %1").arg(modelType), "info");
    addLogMessage(QString("源文件: %1").arg(modelPath), "info");

    // 使用原始的简单转换逻辑（参考autobit项目）
    startSimpleConversion(modelPath, modelType, fileName);
}

// 其他方法的实现将在下一个文件中继续...

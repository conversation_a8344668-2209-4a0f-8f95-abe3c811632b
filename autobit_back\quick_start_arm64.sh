#!/bin/bash
# autobit_back ARM64 Debian 快速启动脚本
# 一键安装所有依赖并构建项目

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检测架构
ARCH=$(uname -m)
if [ "$ARCH" != "aarch64" ]; then
    echo_error "此脚本仅支持ARM64架构，当前架构: $ARCH"
    exit 1
fi

echo_info "=== autobit_back ARM64 Debian 快速部署 ==="
echo_info "检测到ARM64架构，开始自动部署..."

# 更新系统
echo_info "更新系统包..."
sudo apt update

# 安装基础依赖
echo_info "安装基础依赖..."
sudo apt install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    ca-certificates \
    qtbase5-dev \
    qt5-qmake \
    qtbase5-dev-tools \
    libqt5widgets5 \
    libqt5gui5 \
    libqt5core5a \
    libopencv-dev \
    docker.io \
    pkg-config

# 启动Docker服务
echo_info "配置Docker服务..."
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER

# 构建ModelConverter
echo_info "构建ModelConverter Qt应用..."
cd ModelConverter
mkdir -p build
cd build

qmake ../ModelConverter.pro
make -j$(nproc)

if [ -f "ModelConverter" ]; then
    chmod +x ModelConverter
    echo_success "ModelConverter构建成功!"
else
    echo_error "ModelConverter构建失败!"
    exit 1
fi

cd ../../

# 构建Docker镜像
echo_info "构建Docker镜像..."
docker build -t model-converter:latest .

if [ $? -eq 0 ]; then
    echo_success "Docker镜像构建成功!"
else
    echo_error "Docker镜像构建失败!"
    exit 1
fi

# 创建启动脚本
echo_info "创建启动脚本..."
cat > run_modelconverter.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")/ModelConverter/build"
export LD_LIBRARY_PATH="/usr/lib/aarch64-linux-gnu/qt5/lib:${LD_LIBRARY_PATH}"
./ModelConverter
EOF

chmod +x run_modelconverter.sh

# 创建YOLO转换脚本
cat > run_yolo_conversion.sh << 'EOF'
#!/bin/bash
# YOLO模型转换脚本
# 用法: ./run_yolo_conversion.sh <model_type> <weight_file> <output_dir>
# 示例: ./run_yolo_conversion.sh yolov8 models/yolov8n.pt result

MODEL_TYPE=${1:-yolov8}
WEIGHT_FILE=${2:-models/yolov8n.pt}
OUTPUT_DIR=${3:-result}

echo "开始YOLO模型转换..."
echo "模型类型: $MODEL_TYPE"
echo "权重文件: $WEIGHT_FILE"
echo "输出目录: $OUTPUT_DIR"

docker run --rm -it \
  -v "$(pwd)/yolo-rk3588:/workspace/yolo-rk3588" \
  -w /workspace/yolo-rk3588 \
  model-converter:latest \
  --model "$MODEL_TYPE" --weight "$WEIGHT_FILE" --dataset datasets/COCO --output "$OUTPUT_DIR"
EOF

chmod +x run_yolo_conversion.sh

echo_success "=== 部署完成! ==="
echo_info "可用的启动脚本:"
echo "  - ./run_modelconverter.sh     # 启动ModelConverter Qt应用"
echo "  - ./run_yolo_conversion.sh    # 运行YOLO模型转换"
echo "  - ./start_autobit_back.sh     # 完整功能菜单"

echo_info "快速测试:"
echo "1. 启动ModelConverter: ./run_modelconverter.sh"
echo "2. 测试Docker镜像: docker run --rm model-converter:latest --help"

echo_warning "注意: 如果遇到Docker权限问题，请重新登录或运行: newgrp docker"

echo_success "部署完成! 项目已准备就绪。"

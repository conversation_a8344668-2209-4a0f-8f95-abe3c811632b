#!/bin/bash
# 模型转换启动脚本

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

if [ -f "model-converter.tar" ]; then
    echo_info "正在加载镜像文件 model-converter.tar..."
    docker load -i model-converter.tar
    echo_success "Docker镜像加载完成"
else
    echo_error "未找到 model-converter.tar 文件"
    echo_info "请确保 model-converter.tar 文件在当前目录中"
    exit 1
fi

if docker images | grep -q "model-converter"; then
    echo_success "Docker镜像验证成功"
    docker images | grep model-converter
else
    echo_error "Docker镜像加载失败"
    exit 1
fi

# 设置工作目录
cd ModelConverter/build

# 设置Qt环境变量
export LD_LIBRARY_PATH=.:$LD_LIBRARY_PATH

# 启动应用程序
echo "启动模型转换程序..."
./ModelConverter

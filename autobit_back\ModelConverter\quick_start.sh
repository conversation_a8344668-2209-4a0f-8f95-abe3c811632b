#!/bin/bash
# ModelConverter 快速启动脚本

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

echo_info "=== ModelConverter 快速启动 ==="

# 进入脚本所在目录
cd "$(dirname "$0")"

# 检查是否已编译
if [ ! -f "build/ModelConverter" ] && [ ! -f "build/modelconverter" ]; then
    echo_info "首次运行，正在编译..."
    mkdir -p build
    cd build
    qmake ../ModelConverter.pro
    make -j$(nproc)
    cd ..
fi

# 查找可执行文件
EXECUTABLE=""
if [ -f "build/ModelConverter" ]; then
    EXECUTABLE="build/ModelConverter"
elif [ -f "build/modelconverter" ]; then
    EXECUTABLE="build/modelconverter"
else
    EXECUTABLE=$(find build -name "*[Mm]odel[Cc]onverter*" -type f -executable | head -1)
fi

if [ -z "$EXECUTABLE" ]; then
    echo "❌ 未找到可执行文件，请检查编译"
    exit 1
fi

echo_success "找到程序: $EXECUTABLE"

# 设置环境
cd build

# 复制许可证库
if [ -f "../libsf-core-ls.so" ] && [ ! -f "libsf-core-ls.so" ]; then
    cp ../libsf-core-ls.so .
fi

# 设置库路径
export LD_LIBRARY_PATH=".:..:/opt/Qt5.12.6/5.12.6/gcc_64/lib:$LD_LIBRARY_PATH"

# 设置X11环境（如果在WSL2中）
if [ -z "$DISPLAY" ] && grep -q microsoft /proc/version 2>/dev/null; then
    export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
    echo_warning "已自动设置DISPLAY=$DISPLAY"
    echo_warning "请确保Windows中的X11服务器正在运行"
fi

# 设置Qt环境
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

echo_info "启动ModelConverter..."
echo_info "测试激活码: E76G-JEQR-EQRA-T7ZW"

# 启动程序
./"$(basename "$EXECUTABLE")"

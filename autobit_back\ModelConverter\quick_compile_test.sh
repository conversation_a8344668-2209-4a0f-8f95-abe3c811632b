#!/bin/bash
# 快速编译测试 - 修复Qt兼容性问题后

echo "=== Qt 5.12兼容性修复后编译测试 ==="

# 进入项目目录
cd "$(dirname "$0")"

echo "1. 检查修复的文件:"
echo "   检查conversionwindow.cpp中的Qt::SkipEmptyParts使用..."
if grep -n "Qt::SkipEmptyParts" conversionwindow.cpp; then
    echo "   ❌ 仍然存在Qt::SkipEmptyParts"
else
    echo "   ✅ 已修复Qt::SkipEmptyParts问题"
fi

if grep -n "QString::SkipEmptyParts" conversionwindow.cpp; then
    echo "   ✅ 已使用QString::SkipEmptyParts"
else
    echo "   ⚠️ 未找到QString::SkipEmptyParts"
fi

echo ""
echo "2. 清理并重新编译:"
rm -rf build
mkdir -p build
cd build

# 复制许可证库
if [ -f "../libsf-core-ls.so" ]; then
    cp ../libsf-core-ls.so .
    echo "   ✅ 许可证库已复制"
fi

# qmake
echo "   执行qmake..."
qmake ../ModelConverter.pro > qmake.log 2>&1

if [ $? -eq 0 ]; then
    echo "   ✅ qmake成功"
    
    # make
    echo "   执行make..."
    make -j$(nproc) > make.log 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   🎉 编译成功!"
        
        # 检查可执行文件
        if [ -f "ModelConverter" ]; then
            echo "   ✅ 可执行文件生成: ModelConverter"
            echo "   文件大小: $(ls -lh ModelConverter | awk '{print $5}')"
            echo "   文件类型: $(file ModelConverter | cut -d: -f2)"
            
            # 检查依赖
            echo ""
            echo "3. 依赖库检查:"
            echo "   Qt5依赖:"
            ldd ModelConverter | grep Qt5 | head -5
            echo "   许可证库依赖:"
            ldd ModelConverter | grep libsf-core-ls || echo "   未找到许可证库依赖"
            
            echo ""
            echo "🚀 编译完成！可以运行程序："
            echo "   cd build"
            echo "   export LD_LIBRARY_PATH=\"/opt/Qt5.12.6/5.12.6/gcc_64/lib:.:$LD_LIBRARY_PATH\""
            echo "   export DISPLAY=172.24.96.1:0"
            echo "   ./ModelConverter"
            
        else
            echo "   ❌ 未找到ModelConverter可执行文件"
            echo "   build目录内容:"
            ls -la | head -10
        fi
        
    else
        echo "   ❌ make失败"
        echo "   最后的错误信息:"
        tail -15 make.log
        echo ""
        echo "   完整日志: cat build/make.log"
    fi
    
else
    echo "   ❌ qmake失败"
    echo "   错误信息:"
    cat qmake.log
fi

echo ""
echo "=== 编译测试完成 ==="

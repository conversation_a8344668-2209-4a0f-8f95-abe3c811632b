#ifndef LICENSEMANAGER_H
#define LICENSEMANAGER_H

#include <QString>
#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QCryptographicHash>
#include <QSettings>

/**
 * 许可证管理器 - 支持硬件绑定的激活系统
 * 
 * 功能特性:
 * 1. 硬件指纹生成 (基于CPU、主板、网卡等硬件信息)
 * 2. 在线激活验证
 * 3. 离线许可证缓存
 * 4. 调试模式支持
 * 5. 设备唯一标识生成
 */
class LicenseManager : public QObject
{
    Q_OBJECT

public:
    enum ActivationResult {
        Success = 0,           // 激活成功
        InvalidLicense,        // 无效的激活码
        NetworkError,          // 网络错误
        ServerError,           // 服务器错误
        HardwareMismatch,      // 硬件不匹配
        LicenseExpired,        // 许可证过期
        LicenseExhausted,      // 许可证次数用尽
        UnknownError          // 未知错误
    };

    struct DeviceInfo {
        QString deviceId;      // 设备唯一标识
        QString cpuId;         // CPU标识
        QString motherboardId; // 主板标识
        QString macAddress;    // 网卡MAC地址
        QString systemInfo;    // 系统信息
        QString hardwareHash;  // 硬件指纹哈希
    };

    struct LicenseInfo {
        QString licenseKey;    // 激活码
        QString deviceId;      // 绑定的设备ID
        QString productId;     // 产品ID
        QString userId;        // 用户ID
        QDateTime issueDate;   // 签发日期
        QDateTime expireDate;  // 过期日期
        int maxActivations;    // 最大激活次数
        int currentActivations;// 当前激活次数
        bool isValid;          // 是否有效
    };

public:
    explicit LicenseManager(QObject *parent = nullptr);
    ~LicenseManager();

    // 设备信息获取
    static DeviceInfo getDeviceInfo();
    static QString generateDeviceId();
    static QString generateHardwareFingerprint();

    // 激活相关
    void activateLicense(const QString &licenseKey);
    void deactivateLicense();
    bool isLicenseValid();
    LicenseInfo getCurrentLicense();

    // 调试模式
    void setDebugMode(bool enabled);
    bool isDebugMode() const;
    void addDebugDevice(const QString &deviceId, const QString &description);
    QStringList getDebugDevices() const;

    // 配置
    void setServerUrl(const QString &url);
    void setProductId(const QString &productId);
    void setTimeout(int seconds);

signals:
    void activationFinished(ActivationResult result, const QString &message);
    void licenseStatusChanged(bool isValid);

private slots:
    void onActivationReplyFinished();
    void onDeactivationReplyFinished();
    void onValidationReplyFinished();

private:
    // 网络请求
    void sendActivationRequest(const QString &licenseKey, const DeviceInfo &deviceInfo);
    void sendDeactivationRequest();
    void sendValidationRequest();

    // 本地存储
    void saveLicenseToLocal(const LicenseInfo &license);
    LicenseInfo loadLicenseFromLocal();
    void clearLocalLicense();

    // 硬件信息获取
    static QString getCpuId();
    static QString getMotherboardId();
    static QString getMacAddress();
    static QString getSystemInfo();

    // 调试模式激活
    ActivationResult performDebugActivation(const QString &licenseKey);

    // 工具函数
    static QString hashString(const QString &input);
    static QJsonObject deviceInfoToJson(const DeviceInfo &info);
    static DeviceInfo deviceInfoFromJson(const QJsonObject &json);
    static QJsonObject licenseInfoToJson(const LicenseInfo &info);
    static LicenseInfo licenseInfoFromJson(const QJsonObject &json);

private:
    QNetworkAccessManager *m_networkManager;
    QSettings *m_settings;
    
    QString m_serverUrl;
    QString m_productId;
    int m_timeout;
    bool m_debugMode;
    
    LicenseInfo m_currentLicense;
    QStringList m_debugDevices;
    
    static const QString SETTINGS_GROUP;
    static const QString DEFAULT_SERVER_URL;
    static const QString DEFAULT_PRODUCT_ID;
    static const int DEFAULT_TIMEOUT;
};

#endif // LICENSEMANAGER_H

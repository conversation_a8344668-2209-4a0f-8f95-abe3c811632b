#!/bin/bash
# 测试编译脚本 - x86_64 Linux版本

echo "=== ModelConverter x86_64 Linux编译测试 ==="

# 检查系统架构
ARCH=$(uname -m)
echo "系统架构: $ARCH"

if [ "$ARCH" != "x86_64" ]; then
    echo "⚠️ 警告: 当前架构为 $ARCH，但配置为x86_64"
fi

# 检查编译工具
echo "检查编译工具..."
which gcc && echo "✅ gcc 可用" || echo "❌ gcc 未找到"
which g++ && echo "✅ g++ 可用" || echo "❌ g++ 未找到"
which make && echo "✅ make 可用" || echo "❌ make 未找到"
which qmake && echo "✅ qmake 可用" || echo "❌ qmake 未找到"

# 检查Qt库
echo "检查Qt库..."
pkg-config --exists Qt5Core && echo "✅ Qt5Core 可用" || echo "❌ Qt5Core 不可用"
pkg-config --exists Qt5Widgets && echo "✅ Qt5Widgets 可用" || echo "❌ Qt5Widgets 不可用"
pkg-config --exists Qt5Network && echo "✅ Qt5Network 可用" || echo "❌ Qt5Network 不可用"

# 检查许可证库文件
echo "检查许可证库..."
if [ -f "libsf-core-ls.so" ]; then
    echo "✅ 许可证库文件存在"
    file libsf-core-ls.so
    if file libsf-core-ls.so | grep -q "x86-64"; then
        echo "✅ 许可证库为x86_64架构，匹配当前配置"
    else
        echo "⚠️ 许可证库架构可能不匹配"
    fi
else
    echo "❌ 许可证库文件不存在: libsf-core-ls.so"
    echo "请确保 libsf-core-ls.so 文件在项目根目录"
fi

# 检查Qt路径
QT_PATH="/opt/Qt5.12.6/5.12.6/gcc_64"
if [ -d "$QT_PATH" ]; then
    echo "✅ Qt路径存在: $QT_PATH"
else
    echo "❌ Qt路径不存在: $QT_PATH"
    echo "请检查 ModelConverter.pro 中的 QMAKE_PREFIX_PATH 设置"
fi

# 清理之前的构建
echo "清理构建目录..."
rm -rf build
mkdir -p build
cd build

# 生成Makefile
echo "生成Makefile..."
qmake ../ModelConverter.pro

if [ $? -eq 0 ]; then
    echo "✅ qmake 成功"

    # 显示编译配置
    echo "编译配置:"
    echo "DEFINES:"
    grep "DEFINES" Makefile | head -3
    echo "LIBS:"
    grep "LIBS" Makefile | head -3

    # 编译
    echo "开始编译..."
    make -j$(nproc) 2>&1 | tee build.log

    if [ $? -eq 0 ]; then
        echo "🎉 编译成功!"
        echo "可执行文件信息:"

        # 查找可执行文件
        if [ -f "ModelConverter" ]; then
            EXECUTABLE="ModelConverter"
        elif [ -f "modelconverter" ]; then
            EXECUTABLE="modelconverter"
        else
            echo "查找可执行文件..."
            EXECUTABLE=$(find . -name "*[Mm]odel[Cc]onverter*" -type f -executable | head -1)
        fi

        if [ -n "$EXECUTABLE" ]; then
            echo "找到可执行文件: $EXECUTABLE"
            ls -la "$EXECUTABLE"
            file "$EXECUTABLE"

            # 检查依赖库
            echo "依赖库检查:"
            ldd "$EXECUTABLE" | grep -E "(Qt5|libsf-core-ls)" || echo "无Qt5或许可证库依赖"

            echo "✅ 编译完成，可执行文件: $EXECUTABLE"
        else
            echo "❌ 未找到可执行文件"
            echo "build目录内容:"
            ls -la
            exit 1
        fi

    else
        echo "❌ 编译失败!"
        echo "错误信息已保存到 build.log"
        echo "最后几行错误:"
        tail -10 build.log
        exit 1
    fi
else
    echo "❌ qmake 失败!"
    cat ../ModelConverter.pro | grep -E "(QMAKE_PREFIX_PATH|LIBS)"
    exit 1
fi

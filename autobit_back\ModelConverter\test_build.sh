#!/bin/bash
# 测试编译脚本

echo "=== 测试编译环境 ==="

# 检查编译工具
echo "检查编译工具..."
which gcc || echo "gcc 未找到"
which g++ || echo "g++ 未找到"
which make || echo "make 未找到"
which qmake || echo "qmake 未找到"

# 检查Qt库
echo "检查Qt库..."
pkg-config --exists Qt5Core && echo "Qt5Core 可用" || echo "Qt5Core 不可用"
pkg-config --exists Qt5Widgets && echo "Qt5Widgets 可用" || echo "Qt5Widgets 不可用"
pkg-config --exists Qt5Network && echo "Qt5Network 可用" || echo "Qt5Network 不可用"

# 创建构建目录
echo "创建构建目录..."
mkdir -p build
cd build

# 生成Makefile
echo "生成Makefile..."
qmake ../ModelConverter.pro

if [ $? -eq 0 ]; then
    echo "qmake 成功"
    
    # 编译
    echo "开始编译..."
    make -j4
    
    if [ $? -eq 0 ]; then
        echo "编译成功!"
        ls -la ModelConverter
    else
        echo "编译失败!"
        exit 1
    fi
else
    echo "qmake 失败!"
    exit 1
fi

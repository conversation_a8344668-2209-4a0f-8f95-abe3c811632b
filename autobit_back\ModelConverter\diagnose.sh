#!/bin/bash
# ModelConverter 诊断脚本

echo "=== ModelConverter 环境诊断 ==="

# 1. 系统信息
echo "1. 系统信息:"
echo "   架构: $(uname -m)"
echo "   内核: $(uname -r)"
echo "   发行版: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")"

# 2. 编译工具检查
echo ""
echo "2. 编译工具:"
for tool in gcc g++ make qmake; do
    if command -v $tool >/dev/null 2>&1; then
        echo "   ✅ $tool: $(which $tool)"
        if [ "$tool" = "qmake" ]; then
            echo "      版本: $(qmake --version | grep Qt)"
        fi
    else
        echo "   ❌ $tool: 未找到"
    fi
done

# 3. Qt库检查
echo ""
echo "3. Qt库检查:"
for lib in Qt5Core Qt5Widgets Qt5Network; do
    if pkg-config --exists $lib 2>/dev/null; then
        echo "   ✅ $lib: $(pkg-config --modversion $lib)"
    else
        echo "   ❌ $lib: 不可用"
    fi
done

# 4. Qt路径检查
echo ""
echo "4. Qt路径检查:"
QT_PATH="/opt/Qt5.12.6/5.12.6/gcc_64"
if [ -d "$QT_PATH" ]; then
    echo "   ✅ Qt路径存在: $QT_PATH"
    echo "   库目录: $(ls -la $QT_PATH/lib | wc -l) 个文件"
else
    echo "   ❌ Qt路径不存在: $QT_PATH"
    echo "   可能的Qt路径:"
    find /usr /opt /media 2>/dev/null | grep -E "qt5?.*gcc" | head -5
fi

# 5. 许可证库检查
echo ""
echo "5. 许可证库检查:"
if [ -f "libsf-core-ls.so" ]; then
    echo "   ✅ 许可证库存在: libsf-core-ls.so"
    echo "   文件信息: $(file libsf-core-ls.so)"
    echo "   大小: $(ls -lh libsf-core-ls.so | awk '{print $5}')"
else
    echo "   ❌ 许可证库不存在: libsf-core-ls.so"
fi

# 6. 项目文件检查
echo ""
echo "6. 项目文件检查:"
for file in ModelConverter.pro main.cpp activationwindow.cpp conversionwindow.cpp; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file"
    fi
done

# 7. 编译状态检查
echo ""
echo "7. 编译状态检查:"
if [ -d "build" ]; then
    echo "   ✅ build目录存在"
    echo "   build目录内容:"
    ls -la build/ | head -10
    
    # 查找可执行文件
    EXECUTABLES=$(find build -type f -executable 2>/dev/null)
    if [ -n "$EXECUTABLES" ]; then
        echo "   可执行文件:"
        echo "$EXECUTABLES" | while read exe; do
            echo "     - $exe ($(file "$exe" | cut -d: -f2))"
        done
    else
        echo "   ❌ 未找到可执行文件"
    fi
else
    echo "   ❌ build目录不存在"
fi

# 8. 环境变量检查
echo ""
echo "8. 环境变量:"
echo "   DISPLAY: ${DISPLAY:-未设置}"
echo "   LD_LIBRARY_PATH: ${LD_LIBRARY_PATH:-未设置}"
echo "   PATH: $(echo $PATH | tr ':' '\n' | grep -E "(qt|Qt)" | head -3)"

# 9. X11检查（如果是GUI模式）
echo ""
echo "9. X11环境:"
if [ -n "$DISPLAY" ]; then
    echo "   DISPLAY已设置: $DISPLAY"
    if command -v xdpyinfo >/dev/null 2>&1; then
        if xdpyinfo >/dev/null 2>&1; then
            echo "   ✅ X11连接正常"
        else
            echo "   ❌ X11连接失败"
        fi
    else
        echo "   ⚠️ xdpyinfo未安装，无法测试X11"
    fi
else
    echo "   ⚠️ DISPLAY未设置"
    if grep -q microsoft /proc/version 2>/dev/null; then
        echo "   检测到WSL2，建议设置DISPLAY"
        NAMESERVER=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}')
        echo "   建议: export DISPLAY=${NAMESERVER}:0"
    fi
fi

# 10. 建议
echo ""
echo "10. 建议操作:"

if ! command -v qmake >/dev/null 2>&1; then
    echo "   📦 安装Qt开发环境:"
    echo "      sudo apt install -y qtbase5-dev qt5-qmake qtbase5-dev-tools"
fi

if [ ! -f "libsf-core-ls.so" ]; then
    echo "   📁 确保许可证库文件在项目根目录"
fi

if [ ! -d "$QT_PATH" ]; then
    echo "   🔧 检查并修正ModelConverter.pro中的Qt路径"
fi

if [ -z "$DISPLAY" ] && grep -q microsoft /proc/version 2>/dev/null; then
    echo "   🖥️ WSL2环境设置X11:"
    echo "      1. 在Windows中启动VcXsrv X11服务器"
    echo "      2. 设置DISPLAY环境变量"
fi

echo ""
echo "=== 诊断完成 ==="
